digraph "packages_langextract" {
rankdir=BT
charset="utf-8"
"langextract" [color="black", label=<langextract>, shape="box", style="solid"];
"langextract.annotation" [color="black", label=<langextract.annotation>, shape="box", style="solid"];
"langextract.chunking" [color="black", label=<langextract.chunking>, shape="box", style="solid"];
"langextract.data" [color="black", label=<langextract.data>, shape="box", style="solid"];
"langextract.data_lib" [color="black", label=<langextract.data_lib>, shape="box", style="solid"];
"langextract.exceptions" [color="black", label=<langextract.exceptions>, shape="box", style="solid"];
"langextract.factory" [color="black", label=<langextract.factory>, shape="box", style="solid"];
"langextract.inference" [color="black", label=<langextract.inference>, shape="box", style="solid"];
"langextract.io" [color="black", label=<langextract.io>, shape="box", style="solid"];
"langextract.progress" [color="black", label=<langextract.progress>, shape="box", style="solid"];
"langextract.prompting" [color="black", label=<langextract.prompting>, shape="box", style="solid"];
"langextract.providers" [color="black", label=<langextract.providers>, shape="box", style="solid"];
"langextract.providers.gemini" [color="black", label=<langextract.providers.gemini>, shape="box", style="solid"];
"langextract.providers.ollama" [color="black", label=<langextract.providers.ollama>, shape="box", style="solid"];
"langextract.providers.openai" [color="black", label=<langextract.providers.openai>, shape="box", style="solid"];
"langextract.providers.registry" [color="black", label=<langextract.providers.registry>, shape="box", style="solid"];
"langextract.resolver" [color="black", label=<langextract.resolver>, shape="box", style="solid"];
"langextract.schema" [color="black", label=<langextract.schema>, shape="box", style="solid"];
"langextract.tokenizer" [color="black", label=<langextract.tokenizer>, shape="box", style="solid"];
"langextract.visualization" [color="black", label=<langextract.visualization>, shape="box", style="solid"];
"langextract" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.annotation" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.factory" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.io" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.prompting" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.resolver" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract" -> "langextract.visualization" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.chunking" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.progress" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.prompting" [arrowhead="open", arrowtail="none"];
"langextract.annotation" -> "langextract.resolver" [arrowhead="open", arrowtail="none"];
"langextract.chunking" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.chunking" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.chunking" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.chunking" -> "langextract.tokenizer" [arrowhead="open", arrowtail="none"];
"langextract.data" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.data" -> "langextract.tokenizer" [arrowhead="open", arrowtail="none"];
"langextract.data_lib" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.data_lib" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.data_lib" -> "langextract.tokenizer" [arrowhead="open", arrowtail="none"];
"langextract.factory" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.factory" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.factory" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.factory" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.factory" -> "langextract.providers.registry" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.providers.gemini" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.providers.ollama" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.providers.openai" [arrowhead="open", arrowtail="none"];
"langextract.inference" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.io" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.io" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.io" -> "langextract.data_lib" [arrowhead="open", arrowtail="none"];
"langextract.io" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.io" -> "langextract.progress" [arrowhead="open", arrowtail="none"];
"langextract.prompting" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.prompting" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.prompting" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.prompting" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.providers.gemini" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.providers.ollama" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.providers.openai" [arrowhead="open", arrowtail="none"];
"langextract.providers" -> "langextract.providers.registry" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.providers.registry" [arrowhead="open", arrowtail="none"];
"langextract.providers.gemini" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.providers.registry" [arrowhead="open", arrowtail="none"];
"langextract.providers.ollama" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.providers.registry" [arrowhead="open", arrowtail="none"];
"langextract.providers.openai" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.providers.registry" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.providers.registry" -> "langextract.inference" [arrowhead="open", arrowtail="none"];
"langextract.providers.registry" -> "langextract.providers" [arrowhead="open", arrowtail="none"];
"langextract.resolver" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.resolver" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.resolver" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.resolver" -> "langextract.schema" [arrowhead="open", arrowtail="none"];
"langextract.resolver" -> "langextract.tokenizer" [arrowhead="open", arrowtail="none"];
"langextract.schema" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.schema" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.tokenizer" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.tokenizer" -> "langextract.exceptions" [arrowhead="open", arrowtail="none"];
"langextract.visualization" -> "langextract" [arrowhead="open", arrowtail="none"];
"langextract.visualization" -> "langextract.data" [arrowhead="open", arrowtail="none"];
"langextract.visualization" -> "langextract.io" [arrowhead="open", arrowtail="none"];
}
