digraph "packages_dc_ai_red_line_review" {
rankdir=BT
charset="utf-8"
"dc_ai_red_line_review" [color="black", label=<dc_ai_red_line_review>, shape="box", style="solid"];
"dc_ai_red_line_review.core" [color="black", label=<dc_ai_red_line_review.core>, shape="box", style="solid"];
"dc_ai_red_line_review.main_pipe" [color="black", label=<dc_ai_red_line_review.main_pipe>, shape="box", style="solid"];
"dc_ai_red_line_review.retrieval_core" [color="black", label=<dc_ai_red_line_review.retrieval_core>, shape="box", style="solid"];
"dc_ai_red_line_review.utils" [color="black", label=<dc_ai_red_line_review.utils>, shape="box", style="solid"];
"dc_ai_red_line_review" -> "dc_ai_red_line_review.main_pipe" [arrowhead="open", arrowtail="none"];
"dc_ai_red_line_review.core" -> "dc_ai_red_line_review.utils" [arrowhead="open", arrowtail="none"];
"dc_ai_red_line_review.main_pipe" -> "dc_ai_red_line_review.core" [arrowhead="open", arrowtail="none"];
"dc_ai_red_line_review.main_pipe" -> "dc_ai_red_line_review.retrieval_core" [arrowhead="open", arrowtail="none"];
"dc_ai_red_line_review.main_pipe" -> "dc_ai_red_line_review.utils" [arrowhead="open", arrowtail="none"];
"dc_ai_red_line_review.retrieval_core" -> "dc_ai_red_line_review.utils" [arrowhead="open", arrowtail="none"];
}
