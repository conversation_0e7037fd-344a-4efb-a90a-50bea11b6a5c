digraph "classes_langextract" {
rankdir=BT
charset="utf-8"
"langextract.resolver.AbstractResolver" [color="black", fontcolor="black", label=<{AbstractResolver|fence_output<br ALIGN="LEFT"/>format_type<br ALIGN="LEFT"/>|<I>align</I>(extractions: Sequence[data.Extraction], source_text: str, token_offset: int, char_offset: int \| None, enable_fuzzy_alignment: bool, fuzzy_alignment_threshold: float, accept_match_lesser: bool): Iterator[data.Extraction]<br ALIGN="LEFT"/><I>resolve</I>(input_text: str): Sequence[data.Extraction]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.data.AlignmentStatus" [color="black", fontcolor="black", label=<{AlignmentStatus|MATCH_EXACT : str<br ALIGN="LEFT"/>MATCH_FUZZY : str<br ALIGN="LEFT"/>MATCH_GREATER : str<br ALIGN="LEFT"/>MATCH_LESSER : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.data.AnnotatedDocument" [color="black", fontcolor="black", label=<{AnnotatedDocument|document_id<br ALIGN="LEFT"/>extractions : list[Extraction] \| None<br ALIGN="LEFT"/>text : str \| None<br ALIGN="LEFT"/>tokenized_text<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.annotation.Annotator" [color="black", fontcolor="black", label=<{Annotator|<br ALIGN="LEFT"/>|annotate_documents(documents: Iterable[data.Document], resolver: resolver_lib.AbstractResolver, max_char_buffer: int, batch_length: int, debug: bool, extraction_passes: int): Iterator[data.AnnotatedDocument]<br ALIGN="LEFT"/>annotate_text(text: str, resolver: resolver_lib.AbstractResolver, max_char_buffer: int, batch_length: int, additional_context: str \| None, debug: bool, extraction_passes: int): data.AnnotatedDocument<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.inference.BaseLanguageModel" [color="black", fontcolor="black", label=<{BaseLanguageModel|<br ALIGN="LEFT"/>|<I>infer</I>(batch_prompts: Sequence[str]): Iterator[Sequence[ScoredOutput]]<br ALIGN="LEFT"/>infer_batch(prompts: Sequence[str], batch_size: int): list[list[ScoredOutput]]<br ALIGN="LEFT"/>parse_output(output: str): Any<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.schema.BaseSchema" [color="black", fontcolor="black", label=<{BaseSchema|<br ALIGN="LEFT"/>|<I>from_examples</I>(examples_data: Sequence[data.ExampleData], attribute_suffix: str): BaseSchema<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.tokenizer.BaseTokenizerError" [color="black", fontcolor="red", label=<{BaseTokenizerError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.CharInterval" [color="black", fontcolor="black", label=<{CharInterval|end_pos : int<br ALIGN="LEFT"/>start_pos : int<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.data.CharInterval" [color="black", fontcolor="black", label=<{CharInterval|end_pos : int \| None<br ALIGN="LEFT"/>start_pos : int \| None<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.chunking.ChunkIterator" [color="black", fontcolor="black", label=<{ChunkIterator|broken_sentence : bool<br ALIGN="LEFT"/>document<br ALIGN="LEFT"/>max_char_buffer : int<br ALIGN="LEFT"/>sentence_iter<br ALIGN="LEFT"/>tokenized_text : str \| tokenizer.TokenizedText<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.schema.Constraint" [color="black", fontcolor="black", label=<{Constraint|constraint_type<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.schema.ConstraintType" [color="black", fontcolor="black", label=<{ConstraintType|NONE : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.io.Dataset" [color="black", fontcolor="black", label=<{Dataset|id_key : str<br ALIGN="LEFT"/>input_path<br ALIGN="LEFT"/>text_key : str<br ALIGN="LEFT"/>|load(delimiter: str): Iterator[data.Document]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.data.Document" [color="black", fontcolor="black", label=<{Document|additional_context : str \| None<br ALIGN="LEFT"/>document_id<br ALIGN="LEFT"/>text : str<br ALIGN="LEFT"/>tokenized_text<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.annotation.DocumentRepeatError" [color="black", fontcolor="red", label=<{DocumentRepeatError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.data.ExampleData" [color="black", fontcolor="black", label=<{ExampleData|extractions : list[Extraction]<br ALIGN="LEFT"/>text : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.data.Extraction" [color="black", fontcolor="black", label=<{Extraction|alignment_status : AlignmentStatus \| None<br ALIGN="LEFT"/>attributes : dict[str, str \| list[str]] \| None<br ALIGN="LEFT"/>char_interval : CharInterval \| None<br ALIGN="LEFT"/>description : str \| None<br ALIGN="LEFT"/>extraction_class : str<br ALIGN="LEFT"/>extraction_index : int \| None<br ALIGN="LEFT"/>extraction_text : str<br ALIGN="LEFT"/>group_index : int \| None<br ALIGN="LEFT"/>token_interval<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.data.FormatType" [color="black", fontcolor="black", label=<{FormatType|JSON : str<br ALIGN="LEFT"/>YAML : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.inference.GeminiLanguageModel" [color="black", fontcolor="black", label=<{GeminiLanguageModel|api_key : NoneType<br ALIGN="LEFT"/>format_type : str<br ALIGN="LEFT"/>gemini_schema : NoneType<br ALIGN="LEFT"/>max_workers : int<br ALIGN="LEFT"/>model_id : str<br ALIGN="LEFT"/>temperature : float<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[ScoredOutput]]<br ALIGN="LEFT"/>parse_output(output: str): Any<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.providers.gemini.GeminiLanguageModel" [color="black", fontcolor="black", label=<{GeminiLanguageModel|api_key : str \| None<br ALIGN="LEFT"/>fence_output : bool<br ALIGN="LEFT"/>format_type<br ALIGN="LEFT"/>gemini_schema : schema.GeminiSchema \| None<br ALIGN="LEFT"/>max_workers : int<br ALIGN="LEFT"/>model_id : str<br ALIGN="LEFT"/>temperature : float<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[inference.ScoredOutput]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.schema.GeminiSchema" [color="black", fontcolor="black", label=<{GeminiSchema|schema_dict<br ALIGN="LEFT"/>|from_examples(examples_data: Sequence[data.ExampleData], attribute_suffix: str): GeminiSchema<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.exceptions.InferenceConfigError" [color="black", fontcolor="red", label=<{InferenceConfigError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.exceptions.InferenceError" [color="black", fontcolor="red", label=<{InferenceError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.inference.InferenceOutputError" [color="black", fontcolor="red", label=<{InferenceOutputError|message : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.exceptions.InferenceRuntimeError" [color="black", fontcolor="red", label=<{InferenceRuntimeError|original : NoneType<br ALIGN="LEFT"/>provider : NoneType<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.inference.InferenceType" [color="black", fontcolor="black", label=<{InferenceType|ITERATIVE : str<br ALIGN="LEFT"/>MULTIPROCESS : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.io.InvalidDatasetError" [color="black", fontcolor="red", label=<{InvalidDatasetError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.InvalidTokenIntervalError" [color="black", fontcolor="red", label=<{InvalidTokenIntervalError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.exceptions.LangExtractError" [color="black", fontcolor="red", label=<{LangExtractError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.factory.ModelConfig" [color="black", fontcolor="black", label=<{ModelConfig|model_id : str \| None<br ALIGN="LEFT"/>provider : str \| None<br ALIGN="LEFT"/>provider_kwargs : dict[str, typing.Any]<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.inference.OllamaLanguageModel" [color="black", fontcolor="black", label=<{OllamaLanguageModel|format_type : str<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[ScoredOutput]]<br ALIGN="LEFT"/>parse_output(output: str): Any<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.providers.ollama.OllamaLanguageModel" [color="black", fontcolor="black", label=<{OllamaLanguageModel|format_type<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[inference.ScoredOutput]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.inference.OpenAILanguageModel" [color="black", fontcolor="black", label=<{OpenAILanguageModel|api_key : NoneType<br ALIGN="LEFT"/>base_url : NoneType<br ALIGN="LEFT"/>format_type : str<br ALIGN="LEFT"/>max_workers : int<br ALIGN="LEFT"/>model_id : str<br ALIGN="LEFT"/>organization : NoneType<br ALIGN="LEFT"/>temperature : float<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[ScoredOutput]]<br ALIGN="LEFT"/>parse_output(output: str): Any<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.providers.openai.OpenAILanguageModel" [color="black", fontcolor="black", label=<{OpenAILanguageModel|api_key : str \| None<br ALIGN="LEFT"/>base_url : str \| None<br ALIGN="LEFT"/>format_type<br ALIGN="LEFT"/>max_workers : int<br ALIGN="LEFT"/>model_id : str<br ALIGN="LEFT"/>organization : str \| None<br ALIGN="LEFT"/>temperature : float<br ALIGN="LEFT"/>|infer(batch_prompts: Sequence[str]): Iterator[Sequence[inference.ScoredOutput]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.prompting.ParseError" [color="black", fontcolor="red", label=<{ParseError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.prompting.PromptBuilderError" [color="black", fontcolor="red", label=<{PromptBuilderError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.prompting.PromptTemplateStructured" [color="black", fontcolor="black", label=<{PromptTemplateStructured|description : str<br ALIGN="LEFT"/>examples : list[data.ExampleData]<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.prompting.QAPromptGenerator" [color="black", fontcolor="black", label=<{QAPromptGenerator|answer_prefix : str<br ALIGN="LEFT"/>attribute_suffix : str<br ALIGN="LEFT"/>examples_heading : str<br ALIGN="LEFT"/>fence_output : bool<br ALIGN="LEFT"/>format_type<br ALIGN="LEFT"/>question_prefix : str<br ALIGN="LEFT"/>template<br ALIGN="LEFT"/>|format_example_as_text(example: data.ExampleData): str<br ALIGN="LEFT"/>render(question: str, additional_context: str \| None): str<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.resolver.Resolver" [color="black", fontcolor="black", label=<{Resolver|extraction_attributes_suffix : str \| None<br ALIGN="LEFT"/>extraction_index_suffix : str \| None<br ALIGN="LEFT"/>format_type<br ALIGN="LEFT"/>|align(extractions: Sequence[data.Extraction], source_text: str, token_offset: int, char_offset: int \| None, enable_fuzzy_alignment: bool, fuzzy_alignment_threshold: float, accept_match_lesser: bool): Iterator[data.Extraction]<br ALIGN="LEFT"/>extract_ordered_extractions(extraction_data: Sequence[Mapping[str, ExtractionValueType]]): Sequence[data.Extraction]<br ALIGN="LEFT"/>resolve(input_text: str, suppress_parse_errors: bool): Sequence[data.Extraction]<br ALIGN="LEFT"/>string_to_extraction_data(input_string: str): Sequence[Mapping[str, ExtractionValueType]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.resolver.ResolverParsingError" [color="black", fontcolor="red", label=<{ResolverParsingError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.inference.ScoredOutput" [color="black", fontcolor="black", label=<{ScoredOutput|output : str \| None<br ALIGN="LEFT"/>score : float \| None<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.chunking.SentenceIterator" [color="black", fontcolor="black", label=<{SentenceIterator|curr_token_pos : int<br ALIGN="LEFT"/>token_len<br ALIGN="LEFT"/>tokenized_text<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.SentenceRangeError" [color="black", fontcolor="red", label=<{SentenceRangeError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.visualization.SpanPoint" [color="black", fontcolor="black", label=<{SpanPoint|extraction<br ALIGN="LEFT"/>position : int<br ALIGN="LEFT"/>span_idx : int<br ALIGN="LEFT"/>tag_type<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.visualization.TagType" [color="black", fontcolor="black", label=<{TagType|END : str<br ALIGN="LEFT"/>START : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.chunking.TextChunk" [color="black", fontcolor="black", label=<{TextChunk|additional_context<br ALIGN="LEFT"/>char_interval<br ALIGN="LEFT"/>chunk_text<br ALIGN="LEFT"/>document : data.Document \| None<br ALIGN="LEFT"/>document_id<br ALIGN="LEFT"/>document_text<br ALIGN="LEFT"/>sanitized_chunk_text<br ALIGN="LEFT"/>token_interval<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.Token" [color="black", fontcolor="black", label=<{Token|char_interval<br ALIGN="LEFT"/>first_token_after_newline : bool<br ALIGN="LEFT"/>index : int<br ALIGN="LEFT"/>token_type<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.TokenInterval" [color="black", fontcolor="black", label=<{TokenInterval|end_index : int<br ALIGN="LEFT"/>start_index : int<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.TokenType" [color="black", fontcolor="black", label=<{TokenType|ACRONYM : int<br ALIGN="LEFT"/>NUMBER : int<br ALIGN="LEFT"/>PUNCTUATION : int<br ALIGN="LEFT"/>WORD : int<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.chunking.TokenUtilError" [color="black", fontcolor="red", label=<{TokenUtilError|<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.tokenizer.TokenizedText" [color="black", fontcolor="black", label=<{TokenizedText|text : str<br ALIGN="LEFT"/>tokens : list[Token]<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.resolver.WordAligner" [color="black", fontcolor="black", label=<{WordAligner|extraction_tokens : Sequence[str] \| Iterator[str], Sequence[str] \| None<br ALIGN="LEFT"/>matcher<br ALIGN="LEFT"/>source_tokens : Sequence[str] \| Iterator[str], Sequence[str] \| None<br ALIGN="LEFT"/>|align_extractions(extraction_groups: Sequence[Sequence[data.Extraction]], source_text: str, token_offset: int, char_offset: int, delim: str, enable_fuzzy_alignment: bool, fuzzy_alignment_threshold: float, accept_match_lesser: bool): Sequence[Sequence[data.Extraction]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"langextract.providers.registry._Entry" [color="black", fontcolor="black", label=<{_Entry|loader : typing.Callable[[], type[inference.BaseLanguageModel]]<br ALIGN="LEFT"/>patterns : tuple[re.Pattern[str], ...]<br ALIGN="LEFT"/>priority : int<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"langextract.annotation.DocumentRepeatError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.chunking.TokenUtilError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.exceptions.InferenceConfigError" -> "langextract.exceptions.InferenceError" [arrowhead="empty", arrowtail="none"];
"langextract.exceptions.InferenceError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.exceptions.InferenceRuntimeError" -> "langextract.exceptions.InferenceError" [arrowhead="empty", arrowtail="none"];
"langextract.inference.GeminiLanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.inference.InferenceOutputError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.inference.OllamaLanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.inference.OpenAILanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.io.InvalidDatasetError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.prompting.ParseError" -> "langextract.prompting.PromptBuilderError" [arrowhead="empty", arrowtail="none"];
"langextract.prompting.PromptBuilderError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.providers.gemini.GeminiLanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.providers.ollama.OllamaLanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.providers.openai.OpenAILanguageModel" -> "langextract.inference.BaseLanguageModel" [arrowhead="empty", arrowtail="none"];
"langextract.resolver.Resolver" -> "langextract.resolver.AbstractResolver" [arrowhead="empty", arrowtail="none"];
"langextract.resolver.ResolverParsingError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.schema.GeminiSchema" -> "langextract.schema.BaseSchema" [arrowhead="empty", arrowtail="none"];
"langextract.tokenizer.BaseTokenizerError" -> "langextract.exceptions.LangExtractError" [arrowhead="empty", arrowtail="none"];
"langextract.tokenizer.InvalidTokenIntervalError" -> "langextract.tokenizer.BaseTokenizerError" [arrowhead="empty", arrowtail="none"];
"langextract.tokenizer.SentenceRangeError" -> "langextract.tokenizer.BaseTokenizerError" [arrowhead="empty", arrowtail="none"];
"langextract.chunking.SentenceIterator" -> "langextract.chunking.ChunkIterator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="sentence_iter", style="solid"];
"langextract.chunking.SentenceIterator" -> "langextract.chunking.ChunkIterator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="sentence_iter", style="solid"];
"langextract.chunking.SentenceIterator" -> "langextract.chunking.ChunkIterator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="sentence_iter", style="solid"];
"langextract.chunking.SentenceIterator" -> "langextract.chunking.ChunkIterator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="sentence_iter", style="solid"];
"langextract.data.Document" -> "langextract.chunking.ChunkIterator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="document", style="solid"];
"langextract.data.Extraction" -> "langextract.visualization.SpanPoint" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="extraction", style="solid"];
"langextract.data.FormatType" -> "langextract.inference.GeminiLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.inference.OllamaLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.inference.OpenAILanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.prompting.QAPromptGenerator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.providers.gemini.GeminiLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.providers.ollama.OllamaLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.providers.openai.OpenAILanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.prompting.PromptTemplateStructured" -> "langextract.prompting.QAPromptGenerator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="template", style="solid"];
"langextract.prompting.QAPromptGenerator" -> "langextract.annotation.Annotator" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_prompt_generator", style="solid"];
"langextract.providers.gemini.GeminiLanguageModel" -> "langextract.inference.GeminiLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_impl", style="solid"];
"langextract.providers.ollama.OllamaLanguageModel" -> "langextract.inference.OllamaLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_impl", style="solid"];
"langextract.providers.openai.OpenAILanguageModel" -> "langextract.inference.OpenAILanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_impl", style="solid"];
"langextract.schema.Constraint" -> "langextract.inference.OllamaLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_constraint", style="solid"];
"langextract.schema.Constraint" -> "langextract.providers.ollama.OllamaLanguageModel" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="_constraint", style="solid"];
"langextract.schema.ConstraintType" -> "langextract.schema.Constraint" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="constraint_type", style="solid"];
"langextract.tokenizer.CharInterval" -> "langextract.tokenizer.Token" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="char_interval", style="solid"];
"langextract.tokenizer.TokenInterval" -> "langextract.chunking.TextChunk" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="token_interval", style="solid"];
"langextract.tokenizer.TokenType" -> "langextract.tokenizer.Token" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="token_type", style="solid"];
"langextract.visualization.TagType" -> "langextract.visualization.SpanPoint" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="tag_type", style="solid"];
"langextract.data.FormatType" -> "langextract.resolver.AbstractResolver" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="_format_type", style="solid"];
"langextract.data.FormatType" -> "langextract.resolver.Resolver" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="format_type", style="solid"];
"langextract.inference.BaseLanguageModel" -> "langextract.annotation.Annotator" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="_language_model", style="solid"];
"langextract.schema.Constraint" -> "langextract.inference.BaseLanguageModel" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="_constraint", style="solid"];
"langextract.schema.Constraint" -> "langextract.resolver.AbstractResolver" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="_constraint", style="solid"];
"langextract.tokenizer.TokenizedText" -> "langextract.chunking.SentenceIterator" [arrowhead="odiamond", arrowtail="none", fontcolor="green", label="tokenized_text", style="solid"];
}
