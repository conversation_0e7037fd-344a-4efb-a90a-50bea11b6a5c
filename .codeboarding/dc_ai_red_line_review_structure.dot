digraph "classes_dc_ai_red_line_review" {
rankdir=BT
charset="utf-8"
"dc_ai_red_line_review.main_pipe.BasicPipeline" [color="black", fontcolor="black", label=<{BasicPipeline|core_components<br ALIGN="LEFT"/>logger<br ALIGN="LEFT"/>model_client<br ALIGN="LEFT"/>prompt_dict<br ALIGN="LEFT"/>retrieval_core<br ALIGN="LEFT"/>|attach_matched_ids(review_res, messages)<br ALIGN="LEFT"/>run(messages: list, caseId: str)<br ALIGN="LEFT"/>run_sync(content)<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"dc_ai_red_line_review.utils.ChonkieTextChunker" [color="black", fontcolor="black", label=<{ChonkieTextChunker|chunk_overlap : int<br ALIGN="LEFT"/>chunk_size : int<br ALIGN="LEFT"/>|chunk_messages(messages: list, token_limit: int): tuple[list[list], list[int]]<br ALIGN="LEFT"/>chunk_text(text: str): tuple[list[str], list[int]]<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"dc_ai_red_line_review.core.CoreComponents" [color="black", fontcolor="black", label=<{CoreComponents|logger<br ALIGN="LEFT"/>model_client<br ALIGN="LEFT"/>prompt_dict : NoneType<br ALIGN="LEFT"/>|government_inquiry_review(content: str, government_config: dict): list<br ALIGN="LEFT"/>key_contact_review(content: str, risk_keywords: list): dict<br ALIGN="LEFT"/>unified_all_review_sync(content: str, content_tokens: int): dict<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"dc_ai_red_line_review.main_pipe.MessageItem" [color="black", fontcolor="black", label=<{MessageItem|id : int<br ALIGN="LEFT"/>msg : str<br ALIGN="LEFT"/>type : str<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"dc_ai_red_line_review.retrieval_core.RetrievalCore" [color="black", fontcolor="black", label=<{RetrievalCore|chunker<br ALIGN="LEFT"/>doc_embedder<br ALIGN="LEFT"/>doc_store<br ALIGN="LEFT"/>embed_config : dict<br ALIGN="LEFT"/>retriever<br ALIGN="LEFT"/>text_embedder<br ALIGN="LEFT"/>|delete_all_documents()<br ALIGN="LEFT"/>delete_documents_by_case(case_id: str)<br ALIGN="LEFT"/>insert_documents(chunk_docs)<br ALIGN="LEFT"/>search(query: str, filters: dict, top_k: int)<br ALIGN="LEFT"/>}>, shape="record", style="solid"];
"dc_ai_red_line_review.core.CoreComponents.unified_all_review_sync.UnifiedAllReviewRes" [color="black", fontcolor="black", label=<{UnifiedAllReviewRes|consulting_company_info : dict<br ALIGN="LEFT"/>major_complaints : dict<br ALIGN="LEFT"/>negative_news : dict<br ALIGN="LEFT"/>request_contact_information : dict<br ALIGN="LEFT"/>selling_user_info : dict<br ALIGN="LEFT"/>spam_messages : dict<br ALIGN="LEFT"/>|}>, shape="record", style="solid"];
"dc_ai_red_line_review.core.CoreComponents" -> "dc_ai_red_line_review.main_pipe.BasicPipeline" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="core_components", style="solid"];
"dc_ai_red_line_review.retrieval_core.RetrievalCore" -> "dc_ai_red_line_review.main_pipe.BasicPipeline" [arrowhead="diamond", arrowtail="none", fontcolor="green", label="retrieval_core", style="solid"];
}
