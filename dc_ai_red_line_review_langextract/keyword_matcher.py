from typing import Any

from dc_ai_red_line_review_langextract.utils import get_logger, timing_decorator


class KeywordMatcher:
    """Handles keyword-based matching for specific categories."""

    def __init__(self):
        self.logger = get_logger(module_name="keyword_matcher")

    @staticmethod
    def key_contact_review(content: str, risk_keywords: list[str]) -> dict[str, Any]:
        """Sync key contact review function.

        Args:
            content: Text content to search
            risk_keywords: List of keywords to match

        Returns:
            Dictionary with hit_rule and values
        """
        content_lower = content.lower()
        hit_keywords = [
            keyword for keyword in risk_keywords if keyword.lower() in content_lower
        ]

        result = {
            "hit_rule": True if hit_keywords else False,
            "values": hit_keywords,
        }
        return result

    @timing_decorator
    def government_inquiry_review(
        self, content: str, government_config: dict[str, list[str]]
    ) -> list[dict[str, Any]]:
        """Government inquiry review function.

        Args:
            content: Text content to search
            government_config: Configuration with government_agency and government_mailbox lists

        Returns:
            List of results for government agencies and mailboxes
        """
        results = []

        # Check government agencies
        agencies = government_config.get("government_agency", [])
        agency_hits = [
            agency for agency in agencies if agency.lower() in content.lower()
        ]
        results.append(
            {
                "hit_rule": True if agency_hits else False,
                "values": agency_hits,
                "type": "政府机构",
            }
        )

        # Check government mailboxes
        mailboxes = government_config.get("government_mailbox", [])
        mailbox_hits = [
            mailbox for mailbox in mailboxes if mailbox.lower() in content.lower()
        ]
        results.append(
            {
                "hit_rule": True if mailbox_hits else False,
                "values": mailbox_hits,
                "type": "政府邮箱",
            }
        )

        self.logger.info(f"Government inquiry results: {results}")
        return results

    def add_matched_ids_to_keyword_results(
        self, keyword_results: dict[str, Any], messages: list[dict[str, Any]]
    ) -> dict[str, Any]:
        """Add matched_ids to keyword matching results.

        Args:
            keyword_results: Results from keyword matching
            messages: Original message list

        Returns:
            Updated results with matched_ids
        """
        from utils import find_text_in_messages

        if isinstance(keyword_results, dict) and "values" in keyword_results:
            matched_ids = []
            for value in keyword_results["values"]:
                ids = find_text_in_messages(value, messages)
                matched_ids.append(ids)
            keyword_results["matched_ids"] = matched_ids

        elif isinstance(keyword_results, list):
            for item in keyword_results:
                if isinstance(item, dict) and "values" in item:
                    matched_ids = []
                    for value in item["values"]:
                        ids = find_text_in_messages(value, messages)
                        matched_ids.append(ids)
                    item["matched_ids"] = matched_ids

        return keyword_results
