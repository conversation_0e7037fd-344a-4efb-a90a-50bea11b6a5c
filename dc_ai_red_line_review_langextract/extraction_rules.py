import textwrap

import langextract as lx


def get_extraction_prompt() -> str:
    """Get the main extraction prompt for sensitive content detection."""
    return textwrap.dedent("""\
        Extract sensitive content from customer service conversations.
        Identify and extract text that matches the following categories:
        
        1. **consulting_company_info**: Company information inquiries (address, location, licensing)
        2. **selling_user_info**: Offers to buy/sell user data or account information  
        3. **negative_news**: Inquiries about negative rumors or scandals about the company
        4. **major_complaints**: Extremely negative complaints with threats of exposure
        5. **request_contact_information**: Requests for personal contact details
        6. **spam_messages**: Abusive or offensive language from customer service
        
        Extract EXACT text from the conversation. Do not paraphrase or modify.
        Preserve original language (Chinese, English, etc.).
        """)


def get_extraction_examples() -> list[lx.data.ExampleData]:
    """Get example data for training the extraction model."""
    return [
        lx.data.ExampleData(
            text=textwrap.dedent("""\
                [ID:7][TYPE:USER] Hi 我要報稅 機關那邊要我提供交易所主體所在地
                [ID:8][TYPE:AGENT] 您好，歡迎使用KuCoin在綫支持，我是Natalie，很高興爲您服務。關於您的問題，請給我幾分鐘的時間爲您核實，感謝您的耐心等待。
                [ID:10][TYPE:AGENT] 您好，請問您是想咨詢KuCoin的注冊地址嗎？
                [ID:14][TYPE:AGENT] KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé , Republic of Seychelles
                """),
            extractions=[
                lx.data.Extraction(
                    extraction_class="consulting_company_info",
                    extraction_text="我要報稅 機關那邊要我提供交易所主體所在地",
                    attributes={"type": "company_location_inquiry", "language": "chinese"},
                ),
                lx.data.Extraction(
                    extraction_class="consulting_company_info", 
                    extraction_text="請問您是想咨詢KuCoin的注冊地址嗎？",
                    attributes={"type": "company_address_confirmation", "language": "chinese"},
                ),
                lx.data.Extraction(
                    extraction_class="consulting_company_info",
                    extraction_text="KuCoin的注册地址为 Vistra Corporate Services Centre, Suite 23, 1st Floor, Eden Plaza, Eden Island, Mahé , Republic of Seychelles",
                    attributes={"type": "company_address_disclosure", "language": "chinese"},
                ),
            ],
        ),
        lx.data.ExampleData(
            text=textwrap.dedent("""\
                [ID:1][TYPE:USER] I heard KuCoin is going bankrupt, is this true?
                [ID:2][TYPE:AGENT] Thank you for contacting us. These are just rumors without any factual basis.
                [ID:3][TYPE:USER] But I saw news about asset transfers and regulatory issues
                """),
            extractions=[
                lx.data.Extraction(
                    extraction_class="negative_news",
                    extraction_text="I heard KuCoin is going bankrupt, is this true?",
                    attributes={"type": "bankruptcy_rumor", "language": "english"},
                ),
                lx.data.Extraction(
                    extraction_class="negative_news",
                    extraction_text="But I saw news about asset transfers and regulatory issues",
                    attributes={"type": "regulatory_concerns", "language": "english"},
                ),
            ],
        ),
        lx.data.ExampleData(
            text=textwrap.dedent("""\
                [ID:1][TYPE:USER] This is terrible service! I'm going to report you to the police!
                [ID:2][TYPE:USER] I will expose this company on social media and contact lawyers!
                [ID:3][TYPE:AGENT] I understand your frustration. Let me help resolve this issue.
                """),
            extractions=[
                lx.data.Extraction(
                    extraction_class="major_complaints",
                    extraction_text="This is terrible service! I'm going to report you to the police!",
                    attributes={"type": "threat_police_report", "language": "english"},
                ),
                lx.data.Extraction(
                    extraction_class="major_complaints",
                    extraction_text="I will expose this company on social media and contact lawyers!",
                    attributes={"type": "threat_public_exposure", "language": "english"},
                ),
            ],
        ),
        lx.data.ExampleData(
            text=textwrap.dedent("""\
                [ID:1][TYPE:AGENT] Can you provide your personal email address?
                [ID:2][TYPE:AGENT] Please share your WhatsApp number for faster support
                [ID:3][TYPE:USER] Why do you need my personal contact information?
                """),
            extractions=[
                lx.data.Extraction(
                    extraction_class="request_contact_information",
                    extraction_text="Can you provide your personal email address?",
                    attributes={"type": "email_request", "language": "english"},
                ),
                lx.data.Extraction(
                    extraction_class="request_contact_information",
                    extraction_text="Please share your WhatsApp number for faster support",
                    attributes={"type": "phone_request", "language": "english"},
                ),
            ],
        ),
    ]


def get_extraction_classes() -> list[str]:
    """Get the list of extraction classes for sensitive content."""
    return [
        "consulting_company_info",
        "selling_user_info", 
        "negative_news",
        "major_complaints",
        "request_contact_information",
        "spam_messages",
    ]
