# Ground Truth数据处理和准确率评估指南

## 概述

本指南提供了完整的解决方案来处理人工标注数据，生成ground truth数据集，并评估模型输出结果的准确率。

## 文件结构

```
scripts/
├── build_ground_truth.py          # 主要的ground truth生成脚本（已增强）
├── simple_evaluate_accuracy.py    # 准确率评估工具
├── create_sample_gt.py            # 创建示例数据的工具
├── quick_structure_check.py       # Excel文件结构分析工具
└── fast_excel_analysis.py         # 快速Excel分析工具

data/gt/
├── gt_1.xlsx, gt_2.xlsx, ...     # 原始人工标注Excel文件
└── processed/
    ├── ground_truth.jsonl         # 生成的ground truth数据
    ├── labels.csv                 # CSV格式的标签数据
    ├── stats.md                   # 数据统计报告
    ├── sample_labels.csv          # 示例ground truth标签
    └── sample_model_results.csv   # 示例模型结果
```

## 支持的小类别

系统支持以下10个小类别的二分类标注：

1. **政府机构** - 涉及政府机构的内容
2. **政府邮箱** - 包含政府邮箱地址
3. **key_contact** - 关键联系人信息
4. **internal_system** - 内部系统相关
5. **咨询公司信息** - 咨询公司相关信息
6. **兜售用户信息** - 兜售用户信息的行为
7. **负面新闻** - 负面新闻内容
8. **重大客诉** - 重大客户投诉
9. **索要联系方式** - 索要联系方式的行为
10. **辱骂信息** - 包含辱骂内容

## 使用步骤

### 1. 生成Ground Truth数据

```bash
# 基础生成（从Excel文件提取数据）
uv run python scripts/build_ground_truth.py --input data/gt --out data/gt/processed

# 调试模式（查看Excel文件结构）
uv run python scripts/build_ground_truth.py --input data/gt --out data/gt/processed --debug
```

**输出文件：**
- `ground_truth.jsonl`: 每行一个案例，包含消息和标注
- `labels.csv`: 标签矩阵，便于机器学习使用
- `stats.md`: 数据统计报告

### 2. 准确率评估

```bash
# 评估模型结果
uv run python scripts/simple_evaluate_accuracy.py \
  --gt data/gt/processed/labels.csv \
  --pred your_model_results.csv \
  --output evaluation_results.json
```

**输入格式要求：**

Ground Truth文件 (CSV格式):
```csv
case_id,政府机构,政府邮箱,key_contact,internal_system,咨询公司信息,兜售用户信息,负面新闻,重大客诉,索要联系方式,辱骂信息
4186212,0,0,1,0,0,0,1,0,0,1
4407402,0,0,0,0,0,0,0,1,0,1
...
```

模型结果文件 (CSV格式):
```csv
case_id,政府机构,政府邮箱,key_contact,internal_system,咨询公司信息,兜售用户信息,负面新闻,重大客诉,索要联系方式,辱骂信息
4186212,0,0,1,0,0,0,1,0,0,1
4407402,0,0,0,0,0,0,0,1,0,0
...
```

### 3. 测试示例

```bash
# 创建示例数据进行测试
uv run python scripts/create_sample_gt.py

# 运行示例评估
uv run python scripts/simple_evaluate_accuracy.py \
  --gt data/gt/processed/sample_labels.csv \
  --pred data/gt/processed/sample_model_results.csv \
  --output data/gt/processed/evaluation_results.json
```

## 评估指标说明

评估工具会计算以下指标：

### 整体指标
- **微平均 (Micro-average)**: 将所有类别的预测合并计算
- **宏平均 (Macro-average)**: 各类别指标的平均值
- **准确率 (Accuracy)**: 正确预测的比例

### 按类别指标
- **Precision (精确率)**: TP / (TP + FP)
- **Recall (召回率)**: TP / (TP + FN)
- **F1-Score**: 2 * Precision * Recall / (Precision + Recall)
- **Support**: 该类别的真实正例数量

### 混淆矩阵
- **TP (True Positive)**: 正确预测为正例
- **TN (True Negative)**: 正确预测为负例
- **FP (False Positive)**: 错误预测为正例
- **FN (False Negative)**: 错误预测为负例

## 人工标注格式支持

脚本支持多种人工标注格式：

### 1. JSON格式 (推荐)
```json
{
  "sensitive_inquiry": [
    {"type": "负面新闻", "hit_rule": true, "values": ["负面内容"]}
  ],
  "government_inquiry": [
    {"type": "政府机构", "hit_rule": true, "values": ["政府部门"]}
  ]
}
```

### 2. 分列格式
每个小类别一列，值为0/1或true/false

### 3. 文本格式
支持多种文本表示：`1/0`, `true/false`, `yes/no`, `是/否`, `有/无`

## 故障排除

### 1. Excel文件读取慢
- Excel文件较大时读取可能很慢，请耐心等待
- 可以使用 `--debug` 参数查看处理进度

### 2. 没有解析到标注
- 检查Excel文件中的列名是否包含关键词：`正确`, `审核`, `标注`, `结果`
- 使用 `scripts/quick_structure_check.py` 分析文件结构

### 3. 评估结果异常
- 确保ground truth和模型结果的case_id匹配
- 检查数据格式是否正确（0/1值）

## 扩展功能

### 添加新的小类别
1. 修改 `SMALL_CLASSES` 列表
2. 更新 `review_res_to_smallclass_labels` 函数
3. 重新生成ground truth数据

### 支持新的标注格式
1. 修改 `parse_annot_cols` 函数
2. 添加新的解析逻辑

## 最佳实践

1. **数据质量**: 确保人工标注的一致性和准确性
2. **版本控制**: 保留不同版本的ground truth数据
3. **定期评估**: 定期使用新的ground truth数据评估模型性能
4. **文档记录**: 记录标注规则和变更历史

## 联系支持

如果遇到问题或需要添加新功能，请：
1. 检查错误日志
2. 使用调试模式运行脚本
3. 提供具体的错误信息和数据样例
