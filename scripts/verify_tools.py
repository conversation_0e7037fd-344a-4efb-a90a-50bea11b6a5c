#!/usr/bin/env python3
"""
验证工具是否正常工作
"""

import pandas as pd
import json
from pathlib import Path

def verify_sample_data():
    """验证示例数据"""
    print("=== 验证示例数据 ===")
    
    # 检查文件是否存在
    gt_file = Path("data/gt/processed/sample_labels.csv")
    pred_file = Path("data/gt/processed/sample_model_results.csv")
    
    if not gt_file.exists():
        print(f"❌ Ground truth文件不存在: {gt_file}")
        return False
    
    if not pred_file.exists():
        print(f"❌ 模型结果文件不存在: {pred_file}")
        return False
    
    # 读取数据
    try:
        gt_df = pd.read_csv(gt_file)
        pred_df = pd.read_csv(pred_file)
        print(f"✅ 成功读取数据文件")
        print(f"   Ground truth: {len(gt_df)} 行")
        print(f"   模型结果: {len(pred_df)} 行")
    except Exception as e:
        print(f"❌ 读取数据文件失败: {e}")
        return False
    
    # 检查列结构
    expected_cols = ['case_id', '政府机构', '政府邮箱', 'key_contact', 'internal_system', 
                    '咨询公司信息', '兜售用户信息', '负面新闻', '重大客诉', '索要联系方式', '辱骂信息']
    
    for col in expected_cols:
        if col not in gt_df.columns:
            print(f"❌ Ground truth缺少列: {col}")
            return False
        if col not in pred_df.columns:
            print(f"❌ 模型结果缺少列: {col}")
            return False
    
    print(f"✅ 列结构正确")
    
    # 检查case_id匹配
    gt_ids = set(gt_df['case_id'])
    pred_ids = set(pred_df['case_id'])
    common_ids = gt_ids & pred_ids
    
    print(f"✅ 共同case_id数量: {len(common_ids)}")
    
    return True

def simple_accuracy_calculation():
    """简单的准确率计算"""
    print("\n=== 简单准确率计算 ===")
    
    try:
        gt_df = pd.read_csv("data/gt/processed/sample_labels.csv")
        pred_df = pd.read_csv("data/gt/processed/sample_model_results.csv")
        
        # 确保按case_id排序
        gt_df = gt_df.sort_values('case_id')
        pred_df = pred_df.sort_values('case_id')
        
        small_classes = ['政府机构', '政府邮箱', 'key_contact', 'internal_system', 
                        '咨询公司信息', '兜售用户信息', '负面新闻', '重大客诉', 
                        '索要联系方式', '辱骂信息']
        
        print(f"{'类别':<15} {'准确率':<10} {'正确/总数'}")
        print("-" * 40)
        
        total_correct = 0
        total_predictions = 0
        
        for col in small_classes:
            correct = (gt_df[col] == pred_df[col]).sum()
            total = len(gt_df)
            accuracy = correct / total if total > 0 else 0
            
            print(f"{col:<15} {accuracy:<10.3f} {correct}/{total}")
            
            total_correct += correct
            total_predictions += total
        
        overall_accuracy = total_correct / total_predictions if total_predictions > 0 else 0
        print("-" * 40)
        print(f"{'整体准确率':<15} {overall_accuracy:<10.3f} {total_correct}/{total_predictions}")
        
        return True
        
    except Exception as e:
        print(f"❌ 计算准确率失败: {e}")
        return False

def verify_ground_truth_structure():
    """验证ground truth文件结构"""
    print("\n=== 验证Ground Truth结构 ===")
    
    gt_file = Path("data/gt/processed/ground_truth.jsonl")
    if not gt_file.exists():
        print(f"❌ Ground truth JSONL文件不存在: {gt_file}")
        return False
    
    try:
        with open(gt_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if first_line:
                data = json.loads(first_line)
                print(f"✅ JSONL文件格式正确")
                print(f"   案例ID: {data.get('case_id')}")
                print(f"   消息数量: {len(data.get('messages', []))}")
                print(f"   是否有标签: {'labels' in data}")
                print(f"   是否有review_res_gt: {'review_res_gt' in data}")
                return True
            else:
                print(f"❌ JSONL文件为空")
                return False
    except Exception as e:
        print(f"❌ 读取JSONL文件失败: {e}")
        return False

def main():
    """主验证函数"""
    print("开始验证Ground Truth和评估工具...")
    
    success = True
    
    # 验证示例数据
    if not verify_sample_data():
        success = False
    
    # 简单准确率计算
    if not simple_accuracy_calculation():
        success = False
    
    # 验证ground truth结构
    if not verify_ground_truth_structure():
        success = False
    
    print(f"\n{'='*50}")
    if success:
        print("✅ 所有验证通过！工具可以正常使用。")
        print("\n下一步:")
        print("1. 检查Excel文件中的人工标注列")
        print("2. 运行 build_ground_truth.py 生成真实的ground truth")
        print("3. 使用 simple_evaluate_accuracy.py 评估模型结果")
    else:
        print("❌ 部分验证失败，请检查错误信息。")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
