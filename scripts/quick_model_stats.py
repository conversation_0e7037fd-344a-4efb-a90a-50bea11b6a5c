#!/usr/bin/env python3
"""
快速统计模型结果
"""

import json
from collections import defaultdict, Counter

def analyze_model_results():
    """分析模型结果统计"""
    
    # 读取结果文件
    with open('results/individual_cases_results_20250811_104748.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 模型结果统计分析")
    print("=" * 60)
    print(f"总案例数: {len(data)}")
    
    # 统计各类别命中情况
    category_hits = defaultdict(int)
    case_hit_counts = []
    
    for item in data:
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        case_hits = 0
        
        # 1. key_contact
        if review_res.get('key_contact', {}).get('hit_rule', False):
            category_hits['key_contact'] += 1
            case_hits += 1
        
        # 2. internal_system
        if review_res.get('internal_system', {}).get('hit_rule', False):
            category_hits['internal_system'] += 1
            case_hits += 1
        
        # 3. government_inquiry
        for item_data in review_res.get('government_inquiry', []):
            if item_data.get('hit_rule', False):
                gov_type = item_data.get('type', '')
                category_hits[f'政府_{gov_type}'] += 1
                case_hits += 1
        
        # 4. sensitive_inquiry
        for item_data in review_res.get('sensitive_inquiry', []):
            if item_data.get('hit_rule', False):
                si_type = item_data.get('type', '')
                category_hits[f'敏感询问_{si_type}'] += 1
                case_hits += 1
        
        # 5. sensitive_reply
        for item_data in review_res.get('sensitive_reply', []):
            if item_data.get('hit_rule', False):
                sr_type = item_data.get('type', '')
                category_hits[f'敏感回复_{sr_type}'] += 1
                case_hits += 1
        
        case_hit_counts.append(case_hits)
    
    # 打印各类别统计
    print(f"\n📈 各类别命中统计:")
    print("-" * 50)
    total_hits = sum(category_hits.values())
    
    for category, count in sorted(category_hits.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / len(data)) * 100
        print(f"{category:<25}: {count:>4} 案例 ({percentage:>5.1f}%)")
    
    # 命中分布统计
    hit_distribution = Counter(case_hit_counts)
    print(f"\n📊 每案例命中数量分布:")
    print("-" * 30)
    for hits in sorted(hit_distribution.keys()):
        count = hit_distribution[hits]
        percentage = (count / len(data)) * 100
        print(f"{hits} 个命中: {count:>4} 案例 ({percentage:>5.1f}%)")
    
    # 总体统计
    cases_with_hits = len([x for x in case_hit_counts if x > 0])
    cases_without_hits = len(data) - cases_with_hits
    
    print(f"\n🎯 总体统计:")
    print("-" * 30)
    print(f"有命中的案例: {cases_with_hits} ({cases_with_hits/len(data)*100:.1f}%)")
    print(f"无命中的案例: {cases_without_hits} ({cases_without_hits/len(data)*100:.1f}%)")
    print(f"总命中次数: {total_hits}")
    print(f"平均每案例命中: {total_hits/len(data):.2f}")
    
    # 高频命中类别
    print(f"\n🔥 命中最多的类别 (Top 5):")
    print("-" * 40)
    top_categories = sorted(category_hits.items(), key=lambda x: x[1], reverse=True)[:5]
    for i, (category, count) in enumerate(top_categories, 1):
        print(f"{i}. {category}: {count} 次")
    
    # 一些具体案例
    print(f"\n📝 命中案例示例:")
    print("-" * 40)
    examples_shown = 0
    for item in data:
        if examples_shown >= 3:
            break
            
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        hits = []
        
        # 收集该案例的所有命中
        if review_res.get('key_contact', {}).get('hit_rule', False):
            hits.append('key_contact')
        
        if review_res.get('internal_system', {}).get('hit_rule', False):
            hits.append('internal_system')
        
        for item_data in review_res.get('sensitive_inquiry', []):
            if item_data.get('hit_rule', False):
                hits.append(f"敏感询问_{item_data.get('type', '')}")
        
        for item_data in review_res.get('sensitive_reply', []):
            if item_data.get('hit_rule', False):
                hits.append(f"敏感回复_{item_data.get('type', '')}")
        
        if hits:
            print(f"案例 {case_id}: {', '.join(hits)}")
            examples_shown += 1
    
    return {
        'total_cases': len(data),
        'cases_with_hits': cases_with_hits,
        'total_hits': total_hits,
        'category_hits': dict(category_hits),
        'hit_distribution': dict(hit_distribution)
    }

if __name__ == "__main__":
    stats = analyze_model_results()
    
    print(f"\n" + "=" * 60)
    print("📋 总结:")
    print(f"你的模型在 {stats['total_cases']} 个案例中:")
    print(f"• 检测到风险的案例: {stats['cases_with_hits']} 个 ({stats['cases_with_hits']/stats['total_cases']*100:.1f}%)")
    print(f"• 总风险检测次数: {stats['total_hits']} 次")
    print(f"• 平均每案例检测: {stats['total_hits']/stats['total_cases']:.2f} 个风险点")
    
    # 最活跃的检测类别
    top_3 = sorted(stats['category_hits'].items(), key=lambda x: x[1], reverse=True)[:3]
    print(f"• 最常检测到的风险类型:")
    for i, (cat, count) in enumerate(top_3, 1):
        print(f"  {i}. {cat}: {count} 次")
