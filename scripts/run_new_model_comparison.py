#!/usr/bin/env python3
"""
运行新模型的LangExtract并与MainPipe做准确率对比
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import importlib.util

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_langextract_module():
    """动态加载LangExtract模块"""
    module_path = Path("long_text/red_line_lx.py")
    if not module_path.exists():
        raise FileNotFoundError(f"LangExtract模块不存在: {module_path}")
    
    spec = importlib.util.spec_from_file_location("red_line_lx", module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def load_test_cases(limit=20):
    """加载测试案例"""
    data_path = Path("data/individual_cases")
    if not data_path.exists():
        print(f"❌ 数据目录不存在: {data_path}")
        return []
    
    cases = []
    json_files = list(data_path.glob("case_*.json"))
    
    print(f"找到 {len(json_files)} 个案例文件，选择前 {limit} 个进行测试")
    
    for json_file in sorted(json_files)[:limit]:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                case_data = json.load(f)
                cases.append(case_data)
        except Exception as e:
            print(f"读取文件 {json_file} 时出错: {e}")
            continue
    
    return cases

def extract_categories_from_result(review_res):
    """从review_res中提取六个类别的标签"""
    categories = ["重大客诉", "索要联系方式", "兜售用户信息", "负面新闻", "辱骂信息", "咨询公司信息"]
    labels = {category: 0 for category in categories}
    
    # 检查 sensitive_inquiry
    for item_data in review_res.get('sensitive_inquiry', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in categories:
                labels[category] = 1
    
    # 检查 sensitive_reply
    for item_data in review_res.get('sensitive_reply', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in categories:
                labels[category] = 1
    
    return labels

def load_mainpipe_results():
    """加载MainPipe的结果"""
    main_pipe_file = "results/individual_cases_results_20250811_104748.json"
    
    if not Path(main_pipe_file).exists():
        print(f"❌ MainPipe结果文件不存在: {main_pipe_file}")
        return {}
    
    results = {}
    with open(main_pipe_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for item in data:
        case_id = str(item.get('case_id', ''))
        if item.get('status') == 'success':
            result = item.get('result', {})
            review_res = result.get('review_res', {})
            labels = extract_categories_from_result(review_res)
            results[case_id] = labels
    
    return results

def run_langextract_on_cases(cases, lx_module):
    """在案例上运行LangExtract"""
    results = []
    success_count = 0
    error_count = 0
    
    print(f"🔍 开始运行LangExtract检测...")
    
    for i, case in enumerate(cases):
        case_id = case.get('case_id', f'unknown_{i}')
        messages = case.get('messages', [])
        
        print(f"处理案例 {i+1}/{len(cases)}: {case_id}")
        
        try:
            # 运行LangExtract检测
            review_result = lx_module.run_review(messages)
            
            result = {
                "case_id": case_id,
                "status": "success",
                "result": {
                    "id": case_id,
                    "review_res": review_result
                },
                "message_count": len(messages),
                "processed_at": datetime.now().isoformat()
            }
            
            results.append(result)
            success_count += 1
            
            # 显示检测结果
            labels = extract_categories_from_result(review_result)
            hits = [cat for cat, val in labels.items() if val == 1]
            if hits:
                print(f"  ✅ 检测到: {', '.join(hits)}")
            else:
                print(f"  ⚪ 无风险检测")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            result = {
                "case_id": case_id,
                "status": "error",
                "error": str(e),
                "message_count": len(messages),
                "processed_at": datetime.now().isoformat()
            }
            results.append(result)
            error_count += 1
    
    print(f"\n📊 LangExtract处理完成:")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    
    return results

def compare_results(langextract_results, mainpipe_results):
    """对比两种方法的结果"""
    categories = ["重大客诉", "索要联系方式", "兜售用户信息", "负面新闻", "辱骂信息", "咨询公司信息"]
    
    # 提取LangExtract结果
    lx_labels = {}
    for item in langextract_results:
        if item.get('status') == 'success':
            case_id = str(item.get('case_id', ''))
            review_res = item.get('result', {}).get('review_res', {})
            labels = extract_categories_from_result(review_res)
            lx_labels[case_id] = labels
    
    # 找到共同案例
    common_cases = set(lx_labels.keys()) & set(mainpipe_results.keys())
    
    if not common_cases:
        print("❌ 没有找到共同的案例进行对比")
        return
    
    print(f"\n📊 对比分析 (基于 {len(common_cases)} 个共同案例):")
    print("=" * 80)
    
    # 统计各类别
    comparison_stats = {}
    
    for category in categories:
        lx_hits = sum(1 for case_id in common_cases if lx_labels[case_id][category] == 1)
        mp_hits = sum(1 for case_id in common_cases if mainpipe_results[case_id][category] == 1)
        
        # 一致性
        agreement = sum(1 for case_id in common_cases 
                       if lx_labels[case_id][category] == mainpipe_results[case_id][category])
        agreement_rate = agreement / len(common_cases)
        
        # 差异
        lx_only = sum(1 for case_id in common_cases 
                     if lx_labels[case_id][category] == 1 and mainpipe_results[case_id][category] == 0)
        mp_only = sum(1 for case_id in common_cases 
                     if lx_labels[case_id][category] == 0 and mainpipe_results[case_id][category] == 1)
        
        comparison_stats[category] = {
            'lx_hits': lx_hits,
            'mp_hits': mp_hits,
            'agreement_rate': agreement_rate,
            'lx_only': lx_only,
            'mp_only': mp_only
        }
    
    # 打印对比表格
    print(f"{'类别':<15} {'LangExtract':<12} {'MainPipe':<10} {'一致性':<8} {'LX独有':<8} {'MP独有':<8}")
    print("-" * 80)
    
    for category in categories:
        stats = comparison_stats[category]
        print(f"{category:<15} {stats['lx_hits']:<12} {stats['mp_hits']:<10} {stats['agreement_rate']:<8.1%} {stats['lx_only']:<8} {stats['mp_only']:<8}")
    
    # 整体统计
    lx_total_hits = sum(sum(lx_labels[case_id].values()) for case_id in common_cases)
    mp_total_hits = sum(sum(mainpipe_results[case_id].values()) for case_id in common_cases)
    
    lx_cases_with_hits = len([case_id for case_id in common_cases if any(lx_labels[case_id].values())])
    mp_cases_with_hits = len([case_id for case_id in common_cases if any(mainpipe_results[case_id].values())])
    
    print(f"\n🎯 整体对比:")
    print("-" * 40)
    print(f"LangExtract 总命中: {lx_total_hits}")
    print(f"MainPipe 总命中: {mp_total_hits}")
    print(f"LangExtract 有命中案例: {lx_cases_with_hits} ({lx_cases_with_hits/len(common_cases)*100:.1f}%)")
    print(f"MainPipe 有命中案例: {mp_cases_with_hits} ({mp_cases_with_hits/len(common_cases)*100:.1f}%)")
    
    # 保存对比结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存LangExtract结果
    lx_output_file = f"results/langextract_new_model_{timestamp}.json"
    with open(lx_output_file, 'w', encoding='utf-8') as f:
        json.dump(langextract_results, f, ensure_ascii=False, indent=2)
    
    # 保存对比结果
    comparison_result = {
        "timestamp": timestamp,
        "common_cases": len(common_cases),
        "langextract_stats": {
            "total_hits": lx_total_hits,
            "cases_with_hits": lx_cases_with_hits,
            "avg_hits_per_case": lx_total_hits / len(common_cases)
        },
        "mainpipe_stats": {
            "total_hits": mp_total_hits,
            "cases_with_hits": mp_cases_with_hits,
            "avg_hits_per_case": mp_total_hits / len(common_cases)
        },
        "category_comparison": comparison_stats
    }
    
    comparison_output_file = f"results/new_model_comparison_{timestamp}.json"
    with open(comparison_output_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 结果已保存:")
    print(f"📄 LangExtract结果: {lx_output_file}")
    print(f"📊 对比分析: {comparison_output_file}")
    
    return comparison_result

def main():
    """主函数"""
    print("🚀 开始新模型准确率对比测试")
    print("=" * 60)
    
    # 检查环境变量
    required_env_vars = ["MODEL_NAME", "QWQ_API_KEY", "QWQ_BASE_URL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {missing_vars}")
        return
    
    print(f"🔧 当前模型: {os.environ.get('MODEL_NAME', 'Unknown')}")
    
    try:
        # 加载模块和数据
        print("📂 加载LangExtract模块...")
        lx_module = load_langextract_module()
        
        print("📂 加载测试案例...")
        test_cases = load_test_cases(limit=20)  # 测试20个案例
        
        if not test_cases:
            print("❌ 没有找到测试案例")
            return
        
        print("📂 加载MainPipe结果...")
        mainpipe_results = load_mainpipe_results()
        
        if not mainpipe_results:
            print("❌ 没有找到MainPipe结果")
            return
        
        # 运行LangExtract
        langextract_results = run_langextract_on_cases(test_cases, lx_module)
        
        # 对比结果
        compare_results(langextract_results, mainpipe_results)
        
    except Exception as e:
        print(f"❌ 运行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
