#!/usr/bin/env python3
"""
分析模型输出结果的分布情况
"""

import json
import pandas as pd
from pathlib import Path
from collections import defaultdict, Counter

def analyze_json_results(json_file: str):
    """分析JSON格式的模型结果"""
    print(f"分析文件: {json_file}")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"总案例数: {len(data)}")
    
    # 统计各类别的命中情况
    category_stats = defaultdict(int)
    hit_distribution = defaultdict(int)
    
    for item in data:
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        total_hits = 0
        
        # 分析各个类别
        categories = {
            'key_contact': review_res.get('key_contact', {}),
            'internal_system': review_res.get('internal_system', {}),
            'government_inquiry': review_res.get('government_inquiry', []),
            'sensitive_inquiry': review_res.get('sensitive_inquiry', []),
            'sensitive_reply': review_res.get('sensitive_reply', [])
        }
        
        for cat_name, cat_data in categories.items():
            if isinstance(cat_data, dict):
                # key_contact, internal_system类型
                if cat_data.get('hit_rule', False) and cat_data.get('values', []):
                    category_stats[cat_name] += 1
                    total_hits += 1
            elif isinstance(cat_data, list):
                # government_inquiry, sensitive_inquiry, sensitive_reply类型
                for item_data in cat_data:
                    if isinstance(item_data, dict):
                        item_type = item_data.get('type', 'unknown')
                        if item_data.get('hit_rule', False) and item_data.get('values', []):
                            category_stats[f"{cat_name}_{item_type}"] += 1
                            total_hits += 1
        
        hit_distribution[total_hits] += 1
    
    # 打印统计结果
    print(f"\n各类别命中统计:")
    print("-" * 50)
    for category, count in sorted(category_stats.items()):
        percentage = (count / len(data)) * 100
        print(f"{category:<30}: {count:>4} ({percentage:>5.1f}%)")
    
    print(f"\n每个案例的命中数量分布:")
    print("-" * 30)
    for hits, count in sorted(hit_distribution.items()):
        percentage = (count / len(data)) * 100
        print(f"{hits} 个命中: {count:>4} 案例 ({percentage:>5.1f}%)")
    
    # 找出一些有代表性的案例
    print(f"\n案例示例:")
    print("-" * 50)
    
    # 找一个有多个命中的案例
    for item in data[:10]:
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        hits = []
        
        # 检查各类别
        if review_res.get('key_contact', {}).get('hit_rule', False):
            hits.append('key_contact')
        
        if review_res.get('internal_system', {}).get('hit_rule', False):
            hits.append('internal_system')
        
        for gov_item in review_res.get('government_inquiry', []):
            if gov_item.get('hit_rule', False):
                hits.append(f"政府_{gov_item.get('type', '')}")
        
        for si_item in review_res.get('sensitive_inquiry', []):
            if si_item.get('hit_rule', False):
                hits.append(f"敏感询问_{si_item.get('type', '')}")
        
        for sr_item in review_res.get('sensitive_reply', []):
            if sr_item.get('hit_rule', False):
                hits.append(f"敏感回复_{sr_item.get('type', '')}")
        
        if hits:
            print(f"案例 {case_id}: {', '.join(hits)}")
            break
    
    return category_stats, hit_distribution

def analyze_csv_results(csv_file: str):
    """分析CSV格式的模型结果"""
    print(f"分析文件: {csv_file}")
    
    df = pd.read_csv(csv_file)
    success_df = df[df['status'] == 'success']
    
    print(f"总案例数: {len(df)}")
    print(f"成功处理案例数: {len(success_df)}")
    
    if len(success_df) == 0:
        print("没有成功处理的案例")
        return
    
    # 分析result_json列
    category_stats = defaultdict(int)
    
    for _, row in success_df.iterrows():
        try:
            result_json = json.loads(row['result_json'])
            review_res = result_json.get('review_res', {})
            
            # 统计命中情况（简化版）
            if review_res.get('key_contact', {}).get('hit_rule', False):
                category_stats['key_contact'] += 1
            
            if review_res.get('internal_system', {}).get('hit_rule', False):
                category_stats['internal_system'] += 1
            
            # 统计敏感内容
            for si_item in review_res.get('sensitive_inquiry', []):
                if si_item.get('hit_rule', False):
                    category_stats[f"敏感询问_{si_item.get('type', '')}"] += 1
            
            for sr_item in review_res.get('sensitive_reply', []):
                if sr_item.get('hit_rule', False):
                    category_stats[f"敏感回复_{sr_item.get('type', '')}"] += 1
                    
        except (json.JSONDecodeError, Exception) as e:
            continue
    
    print(f"\n各类别命中统计:")
    print("-" * 50)
    for category, count in sorted(category_stats.items()):
        percentage = (count / len(success_df)) * 100
        print(f"{category:<30}: {count:>4} ({percentage:>5.1f}%)")

def main():
    # 分析最新的结果文件
    results_dir = Path("results")
    
    # 找到最新的JSON文件
    json_files = list(results_dir.glob("individual_cases_results_*.json"))
    if json_files:
        latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
        print("="*60)
        print("分析JSON结果文件")
        print("="*60)
        analyze_json_results(latest_json)
    
    # 找到最新的CSV文件
    csv_files = list(results_dir.glob("individual_cases_results_*.csv"))
    if csv_files:
        latest_csv = max(csv_files, key=lambda x: x.stat().st_mtime)
        print("\n" + "="*60)
        print("分析CSV结果文件")
        print("="*60)
        analyze_csv_results(latest_csv)
    
    if not json_files and not csv_files:
        print("未找到结果文件")

if __name__ == "__main__":
    main()
