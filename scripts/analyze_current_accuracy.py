#!/usr/bin/env python3
"""
分析当前模型结果的准确率
基于individual_cases数据和ground truth数据
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import argparse

# 小类别映射（模型输出 -> 标准名称）
CATEGORY_MAPPING = {
    # government_inquiry
    "政府机构": "政府机构",
    "政府邮箱": "政府邮箱",
    
    # key_contact & internal_system
    "key_contact": "key_contact", 
    "internal_system": "internal_system",
    
    # sensitive_inquiry
    "咨询公司信息": "咨询公司信息",
    "兜售用户信息": "兜售用户信息", 
    "负面新闻": "负面新闻",
    "重大客诉": "重大客诉",
    
    # sensitive_reply
    "索要联系方式": "索要联系方式",
    "辱骂信息": "辱骂信息"
}

SMALL_CLASSES = list(CATEGORY_MAPPING.values())

def load_model_results_from_json(json_file: str) -> Dict[str, Dict[str, int]]:
    """从JSON结果文件加载模型结果"""
    model_results = {}
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for item in data:
        case_id = str(item.get('case_id', ''))
        if not case_id:
            continue
            
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        # 转换为小类别标签
        labels = convert_review_res_to_labels(review_res)
        model_results[case_id] = labels
    
    print(f"从JSON文件加载了 {len(model_results)} 个案例的模型结果")
    return model_results

def load_model_results_from_csv(csv_file: str) -> Dict[str, Dict[str, int]]:
    """从CSV结果文件加载模型结果"""
    model_results = {}
    
    df = pd.read_csv(csv_file)
    
    for _, row in df.iterrows():
        case_id = str(row.get('case_id', ''))
        if not case_id or row.get('status') != 'success':
            continue
            
        try:
            result_json = json.loads(row.get('result_json', '{}'))
            review_res = result_json.get('review_res', {})
            
            # 转换为小类别标签
            labels = convert_review_res_to_labels(review_res)
            model_results[case_id] = labels
            
        except (json.JSONDecodeError, Exception) as e:
            print(f"解析案例 {case_id} 的结果时出错: {e}")
            continue
    
    print(f"从CSV文件加载了 {len(model_results)} 个案例的模型结果")
    return model_results

def convert_review_res_to_labels(review_res: Dict[str, Any]) -> Dict[str, int]:
    """将review_res转换为小类别标签"""
    labels = {cls: 0 for cls in SMALL_CLASSES}
    
    # government_inquiry
    gov_inquiry = review_res.get("government_inquiry", [])
    if isinstance(gov_inquiry, list):
        for item in gov_inquiry:
            if isinstance(item, dict):
                category = item.get("type")
                hit_rule = item.get("hit_rule", False)
                values = item.get("values", [])
                
                if category in CATEGORY_MAPPING and hit_rule and values:
                    labels[CATEGORY_MAPPING[category]] = 1
    
    # key_contact & internal_system
    for key in ["key_contact", "internal_system"]:
        item = review_res.get(key, {})
        if isinstance(item, dict):
            hit_rule = item.get("hit_rule", False)
            values = item.get("values", [])
            
            if hit_rule and values:
                labels[key] = 1
    
    # sensitive_inquiry
    sensitive_inquiry = review_res.get("sensitive_inquiry", [])
    if isinstance(sensitive_inquiry, list):
        for item in sensitive_inquiry:
            if isinstance(item, dict):
                category = item.get("type")
                hit_rule = item.get("hit_rule", False)
                values = item.get("values", [])
                
                if category in CATEGORY_MAPPING and hit_rule and values:
                    labels[CATEGORY_MAPPING[category]] = 1
    
    # sensitive_reply
    sensitive_reply = review_res.get("sensitive_reply", [])
    if isinstance(sensitive_reply, list):
        for item in sensitive_reply:
            if isinstance(item, dict):
                category = item.get("type")
                hit_rule = item.get("hit_rule", False)
                values = item.get("values", [])
                
                if category in CATEGORY_MAPPING and hit_rule and values:
                    labels[CATEGORY_MAPPING[category]] = 1
    
    return labels

def load_ground_truth(gt_file: str) -> Dict[str, Dict[str, int]]:
    """加载ground truth数据"""
    gt_labels = {}
    
    if gt_file.endswith('.csv'):
        df = pd.read_csv(gt_file)
        for _, row in df.iterrows():
            case_id = str(row['case_id'])
            labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            gt_labels[case_id] = labels
    
    elif gt_file.endswith('.jsonl'):
        with open(gt_file, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                case_id = str(data.get('case_id', ''))
                labels = data.get('labels', {})
                if case_id and labels:
                    # 确保所有类别都存在
                    full_labels = {cls: labels.get(cls, 0) for cls in SMALL_CLASSES}
                    gt_labels[case_id] = full_labels
    
    print(f"加载了 {len(gt_labels)} 个案例的ground truth标签")
    return gt_labels

def calculate_metrics(gt_labels: Dict[str, Dict[str, int]], 
                     model_labels: Dict[str, Dict[str, int]]) -> Dict[str, Any]:
    """计算评估指标"""
    
    # 找到共同的案例ID
    common_case_ids = set(gt_labels.keys()) & set(model_labels.keys())
    print(f"共同案例数: {len(common_case_ids)}")
    
    if not common_case_ids:
        print("警告: 没有找到共同的案例ID")
        print(f"Ground truth案例ID示例: {list(gt_labels.keys())[:5]}")
        print(f"模型结果案例ID示例: {list(model_labels.keys())[:5]}")
        return {}
    
    metrics = {}
    class_metrics = {}
    
    # 计算每个类别的指标
    overall_tp = overall_tn = overall_fp = overall_fn = 0
    
    for cls in SMALL_CLASSES:
        tp = tn = fp = fn = 0
        
        for case_id in common_case_ids:
            gt_val = gt_labels[case_id][cls]
            pred_val = model_labels[case_id][cls]
            
            if gt_val == 1 and pred_val == 1:
                tp += 1
            elif gt_val == 0 and pred_val == 0:
                tn += 1
            elif gt_val == 0 and pred_val == 1:
                fp += 1
            elif gt_val == 1 and pred_val == 0:
                fn += 1
        
        # 累计到整体指标
        overall_tp += tp
        overall_tn += tn
        overall_fp += fp
        overall_fn += fn
        
        # 计算该类别的指标
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        accuracy = (tp + tn) / (tp + tn + fp + fn) if (tp + tn + fp + fn) > 0 else 0.0
        
        class_metrics[cls] = {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'accuracy': accuracy,
            'tp': tp,
            'tn': tn,
            'fp': fp,
            'fn': fn,
            'support': tp + fn
        }
    
    # 计算整体指标
    overall_precision = overall_tp / (overall_tp + overall_fp) if (overall_tp + overall_fp) > 0 else 0.0
    overall_recall = overall_tp / (overall_tp + overall_fn) if (overall_tp + overall_fn) > 0 else 0.0
    overall_f1 = 2 * overall_precision * overall_recall / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
    overall_accuracy = (overall_tp + overall_tn) / (overall_tp + overall_tn + overall_fp + overall_fn) if (overall_tp + overall_tn + overall_fp + overall_fn) > 0 else 0.0
    
    # 计算宏平均指标
    macro_precision = sum(m['precision'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    macro_recall = sum(m['recall'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    macro_f1 = sum(m['f1'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    
    metrics['overall'] = {
        'micro_precision': overall_precision,
        'micro_recall': overall_recall,
        'micro_f1': overall_f1,
        'micro_accuracy': overall_accuracy,
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'total_cases': len(common_case_ids)
    }
    
    metrics['by_class'] = class_metrics
    
    return metrics

def print_metrics_report(metrics: Dict[str, Any]):
    """打印评估报告"""
    if not metrics:
        print("无法生成评估报告：没有有效的指标数据")
        return
        
    print("\n" + "="*80)
    print("模型准确率评估报告")
    print("="*80)
    
    # 整体指标
    overall = metrics['overall']
    print(f"\n整体指标 (基于 {overall['total_cases']} 个案例):")
    print(f"  微平均 - Precision: {overall['micro_precision']:.4f}, Recall: {overall['micro_recall']:.4f}, F1: {overall['micro_f1']:.4f}")
    print(f"  宏平均 - Precision: {overall['macro_precision']:.4f}, Recall: {overall['macro_recall']:.4f}, F1: {overall['macro_f1']:.4f}")
    print(f"  微平均准确率: {overall['micro_accuracy']:.4f}")
    
    # 按类别的详细指标
    print(f"\n按类别详细指标:")
    print(f"{'类别':<15} {'Precision':<10} {'Recall':<10} {'F1':<10} {'Accuracy':<10} {'Support':<8} {'TP':<4} {'FP':<4} {'FN':<4} {'TN':<4}")
    print("-" * 100)
    
    for cls in SMALL_CLASSES:
        m = metrics['by_class'][cls]
        print(f"{cls:<15} {m['precision']:<10.4f} {m['recall']:<10.4f} {m['f1']:<10.4f} {m['accuracy']:<10.4f} {m['support']:<8} {m['tp']:<4} {m['fp']:<4} {m['fn']:<4} {m['tn']:<4}")

def main():
    parser = argparse.ArgumentParser(description="分析当前模型结果的准确率")
    parser.add_argument("--model-results", required=True, help="模型结果文件路径 (.json 或 .csv)")
    parser.add_argument("--ground-truth", required=True, help="Ground truth文件路径 (.csv 或 .jsonl)")
    parser.add_argument("--output", help="输出详细结果的文件路径 (可选)")
    
    args = parser.parse_args()
    
    try:
        # 加载模型结果
        print("加载模型结果...")
        if args.model_results.endswith('.json'):
            model_labels = load_model_results_from_json(args.model_results)
        elif args.model_results.endswith('.csv'):
            model_labels = load_model_results_from_csv(args.model_results)
        else:
            raise ValueError("不支持的模型结果文件格式，请使用 .json 或 .csv")
        
        # 加载ground truth
        print("加载ground truth数据...")
        gt_labels = load_ground_truth(args.ground_truth)
        
        # 计算指标
        print("计算评估指标...")
        metrics = calculate_metrics(gt_labels, model_labels)
        
        # 打印报告
        print_metrics_report(metrics)
        
        # 保存详细结果
        if args.output and metrics:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(metrics, f, ensure_ascii=False, indent=2)
            print(f"\n详细结果已保存到: {args.output}")
        
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
