#!/usr/bin/env python3
"""
新模型准确率测试脚本
使用方法: uv run python scripts/new_model_accuracy_test.py
"""

import json
import os
import sys
from pathlib import Path
from datetime import datetime

def check_environment():
    """检查环境配置"""
    print("🔧 检查环境配置...")
    
    required_vars = ["MODEL_NAME", "QWQ_API_KEY", "QWQ_BASE_URL"]
    env_status = {}
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            env_status[var] = "✅ 已设置"
            if var == "MODEL_NAME":
                print(f"  当前模型: {value}")
        else:
            env_status[var] = "❌ 未设置"
    
    all_set = all(os.environ.get(var) for var in required_vars)
    
    if not all_set:
        print("\n❌ 环境变量配置不完整:")
        for var, status in env_status.items():
            print(f"  {var}: {status}")
        print("\n请设置以下环境变量:")
        print("export MODEL_NAME='your-model-name'")
        print("export QWQ_API_KEY='your-api-key'")
        print("export QWQ_BASE_URL='your-base-url'")
        return False
    
    print("✅ 环境配置完整")
    return True

def create_test_script():
    """创建测试脚本"""
    test_script = '''
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入LangExtract模块
import importlib.util
spec = importlib.util.spec_from_file_location("red_line_lx", "long_text/red_line_lx.py")
lx_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(lx_module)

# 测试案例
test_cases = [
    {
        "case_id": "test_001",
        "messages": [
            {"id": 1, "type": "USER", "msg": "你们这个垃圾平台，我要投诉到底！"},
            {"id": 2, "type": "AGENT", "msg": "请您冷静，我来帮您处理"}
        ]
    },
    {
        "case_id": "test_002", 
        "messages": [
            {"id": 1, "type": "USER", "msg": "听说KuCoin要跑路了，是真的吗？"},
            {"id": 2, "type": "AGENT", "msg": "这是不实传言，请以官方公告为准"}
        ]
    },
    {
        "case_id": "test_003",
        "messages": [
            {"id": 1, "type": "AGENT", "msg": "请提供您的手机号码和邮箱地址"},
            {"id": 2, "type": "USER", "msg": "为什么需要这些信息？"}
        ]
    }
]

results = []
for case in test_cases:
    try:
        result = lx_module.run_review(case["messages"])
        
        # 统计命中
        hits = []
        for item in result.get("sensitive_inquiry", []):
            if item.get("hit_rule", False):
                hits.append(item.get("type", ""))
        for item in result.get("sensitive_reply", []):
            if item.get("hit_rule", False):
                hits.append(item.get("type", ""))
        
        results.append({
            "case_id": case["case_id"],
            "status": "success",
            "hits": hits,
            "result": result
        })
        
        print(f"案例 {case['case_id']}: {hits if hits else '无命中'}")
        
    except Exception as e:
        results.append({
            "case_id": case["case_id"],
            "status": "error",
            "error": str(e)
        })
        print(f"案例 {case['case_id']}: 错误 - {e}")

# 保存结果
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_file = f"results/new_model_test_{timestamp}.json"
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)

print(f"\\n结果已保存到: {output_file}")
'''
    
    with open("temp_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    return "temp_test.py"

def run_comparison_analysis():
    """运行对比分析"""
    print("\n📊 准备对比分析...")
    
    # 检查MainPipe结果文件
    mainpipe_file = "results/individual_cases_results_20250811_104748.json"
    if not Path(mainpipe_file).exists():
        print(f"❌ MainPipe结果文件不存在: {mainpipe_file}")
        return
    
    print(f"✅ 找到MainPipe结果文件")
    
    # 创建对比分析脚本
    comparison_script = f'''
import json
from datetime import datetime
from pathlib import Path

def load_mainpipe_results():
    """加载MainPipe结果"""
    results = {{}}
    with open("{mainpipe_file}", "r", encoding="utf-8") as f:
        data = json.load(f)
    
    for item in data:
        case_id = str(item.get("case_id", ""))
        if item.get("status") == "success":
            result = item.get("result", {{}})
            review_res = result.get("review_res", {{}})
            
            # 提取六个类别
            labels = {{
                "重大客诉": 0, "索要联系方式": 0, "兜售用户信息": 0,
                "负面新闻": 0, "辱骂信息": 0, "咨询公司信息": 0
            }}
            
            # 检查 sensitive_inquiry
            for item_data in review_res.get("sensitive_inquiry", []):
                if item_data.get("hit_rule", False):
                    category = item_data.get("type", "")
                    if category in labels:
                        labels[category] = 1
            
            # 检查 sensitive_reply
            for item_data in review_res.get("sensitive_reply", []):
                if item_data.get("hit_rule", False):
                    category = item_data.get("type", "")
                    if category in labels:
                        labels[category] = 1
            
            results[case_id] = labels
    
    return results

def analyze_mainpipe_stats():
    """分析MainPipe统计"""
    mp_results = load_mainpipe_results()
    
    categories = ["重大客诉", "索要联系方式", "兜售用户信息", "负面新闻", "辱骂信息", "咨询公司信息"]
    
    print("📊 MainPipe方法统计 (基于现有结果):")
    print("-" * 50)
    
    total_cases = len(mp_results)
    category_stats = {{}}
    
    for category in categories:
        hits = sum(1 for labels in mp_results.values() if labels[category] == 1)
        percentage = (hits / total_cases) * 100 if total_cases > 0 else 0
        category_stats[category] = hits
        print(f"{{category:<15}}: {{hits:>4}} 案例 ({{percentage:>5.1f}}%)")
    
    total_hits = sum(category_stats.values())
    cases_with_hits = len([labels for labels in mp_results.values() if any(labels.values())])
    
    print(f"\\n总体统计:")
    print(f"  总案例数: {{total_cases}}")
    print(f"  有命中案例: {{cases_with_hits}} ({{cases_with_hits/total_cases*100:.1f}}%)")
    print(f"  总命中次数: {{total_hits}}")
    print(f"  平均每案例: {{total_hits/total_cases:.2f}}")
    
    # 保存统计结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    stats = {{
        "method": "MainPipe",
        "timestamp": timestamp,
        "total_cases": total_cases,
        "cases_with_hits": cases_with_hits,
        "total_hits": total_hits,
        "category_stats": category_stats
    }}
    
    output_file = f"results/mainpipe_stats_{{timestamp}}.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(stats, f, ensure_ascii=False, indent=2)
    
    print(f"\\n✅ MainPipe统计已保存到: {{output_file}}")
    return stats

if __name__ == "__main__":
    analyze_mainpipe_stats()
'''
    
    with open("temp_comparison.py", "w", encoding="utf-8") as f:
        f.write(comparison_script)
    
    return "temp_comparison.py"

def create_usage_guide():
    """创建使用指南"""
    guide = """
# 新模型准确率测试指南

## 🚀 快速开始

### 1. 环境准备
```bash
# 设置环境变量
export MODEL_NAME='your-new-model-name'
export QWQ_API_KEY='your-api-key'  
export QWQ_BASE_URL='your-base-url'
```

### 2. 测试新模型
```bash
# 运行基础测试
uv run python temp_test.py

# 查看MainPipe统计
uv run python temp_comparison.py
```

### 3. 完整对比测试
```bash
# 运行完整对比（需要环境配置正确）
uv run python scripts/run_new_model_comparison.py
```

## 📊 预期输出

### 新模型测试结果
- 测试案例的检测结果
- 各类别命中情况
- 错误信息（如有）

### MainPipe统计
- 各类别命中数量和比例
- 总体检测覆盖率
- 平均每案例命中数

### 对比分析
- 两种方法的一致性
- 各类别检测差异
- 性能对比

## 🎯 关键指标

1. **检测准确率**: 正确识别风险内容的比例
2. **检测覆盖率**: 有风险检测的案例比例  
3. **一致性**: 两种方法结果的一致程度
4. **误判率**: 错误检测的比例

## 💡 使用建议

1. **先运行基础测试**: 确保新模型工作正常
2. **小批量对比**: 在少量案例上验证效果
3. **逐步扩大**: 确认效果后扩大测试范围
4. **持续监控**: 建立长期的准确率监控

## 🔧 故障排除

### 环境变量问题
- 检查是否正确设置了所有必需的环境变量
- 确认API密钥有效且有足够额度

### 模块导入问题  
- 确认long_text/red_line_lx.py文件存在
- 检查langextract依赖是否正确安装

### API调用问题
- 检查网络连接
- 确认API端点可访问
- 验证模型名称正确
"""
    
    with open("NEW_MODEL_TEST_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    return "NEW_MODEL_TEST_GUIDE.md"

def main():
    """主函数"""
    print("🚀 新模型准确率测试准备")
    print("=" * 50)
    
    # 1. 检查环境
    if not check_environment():
        print("\n💡 请先配置环境变量，然后重新运行此脚本")
        return
    
    # 2. 创建测试脚本
    print("\n📝 创建测试脚本...")
    test_script = create_test_script()
    print(f"✅ 创建测试脚本: {test_script}")
    
    # 3. 创建对比分析脚本
    print("\n📊 创建对比分析脚本...")
    comparison_script = run_comparison_analysis()
    print(f"✅ 创建对比脚本: {comparison_script}")
    
    # 4. 创建使用指南
    print("\n📖 创建使用指南...")
    guide_file = create_usage_guide()
    print(f"✅ 创建使用指南: {guide_file}")
    
    print(f"\n🎯 下一步操作:")
    print(f"1. 运行基础测试: uv run python {test_script}")
    print(f"2. 查看MainPipe统计: uv run python {comparison_script}")
    print(f"3. 阅读详细指南: cat {guide_file}")
    print(f"4. 运行完整对比: uv run python scripts/run_new_model_comparison.py")

if __name__ == "__main__":
    main()
