#!/usr/bin/env python3
"""
演示LangExtract方法并与现有结果对比
"""

import json
from datetime import datetime

# 模拟LangExtract的结果结构
def simulate_langextract_results():
    """模拟LangExtract方法的检测结果"""
    
    # 基于对LangExtract方法的理解，模拟一些结果
    # LangExtract更注重精确提取，可能会有不同的检测模式
    
    simulated_results = [
        {
            "case_id": "10059303",
            "status": "success",
            "result": {
                "id": "10059303",
                "review_res": {
                    "sensitive_inquiry": [
                        {"hit_rule": False, "values": [], "type": "咨询公司信息"},
                        {"hit_rule": False, "values": [], "type": "兜售用户信息"},
                        {"hit_rule": False, "values": [], "type": "负面新闻"},
                        {"hit_rule": True, "values": ["You went from telling me, you're working on making sure I get my funds speedily, now you're saying 30-60 days. Sounds like a scam"], "type": "重大客诉"}
                    ],
                    "sensitive_reply": [
                        {"hit_rule": False, "values": [], "type": "索要联系方式"},
                        {"hit_rule": False, "values": [], "type": "辱骂信息"}
                    ]
                }
            }
        },
        {
            "case_id": "********", 
            "status": "success",
            "result": {
                "id": "********",
                "review_res": {
                    "sensitive_inquiry": [
                        {"hit_rule": False, "values": [], "type": "咨询公司信息"},
                        {"hit_rule": False, "values": [], "type": "兜售用户信息"},
                        {"hit_rule": False, "values": [], "type": "负面新闻"},
                        {"hit_rule": True, "values": ["IF YOU WONT UNFREEZE MY ACCOUNT IN HOURS MY LAWYER WILL TAKE RHE NECESSARY ACTION"], "type": "重大客诉"}
                    ],
                    "sensitive_reply": [
                        {"hit_rule": False, "values": [], "type": "索要联系方式"},
                        {"hit_rule": True, "values": ["TIRED OF YOUR SHIT SERVICE"], "type": "辱骂信息"}
                    ]
                }
            }
        }
    ]
    
    return simulated_results

def extract_categories_from_result(review_res):
    """从review_res中提取六个类别的标签"""
    categories = ["重大客诉", "索要联系方式", "兜售用户信息", "负面新闻", "辱骂信息", "咨询公司信息"]
    labels = {category: 0 for category in categories}
    
    # 检查 sensitive_inquiry
    for item_data in review_res.get('sensitive_inquiry', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in categories:
                labels[category] = 1
    
    # 检查 sensitive_reply
    for item_data in review_res.get('sensitive_reply', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in categories:
                labels[category] = 1
    
    return labels

def compare_methods():
    """对比两种方法"""
    
    print("📊 LangExtract vs MainPipe 方法对比演示")
    print("=" * 60)
    
    # 模拟LangExtract结果
    langextract_results = simulate_langextract_results()
    
    # 从现有的MainPipe结果中提取对应案例
    main_pipe_file = "results/individual_cases_results_20250811_104748.json"
    
    try:
        with open(main_pipe_file, 'r', encoding='utf-8') as f:
            main_pipe_data = json.load(f)
    except FileNotFoundError:
        print(f"❌ 找不到MainPipe结果文件: {main_pipe_file}")
        return
    
    # 创建MainPipe结果的映射
    main_pipe_map = {}
    for item in main_pipe_data:
        case_id = str(item.get('case_id', ''))
        if item.get('status') == 'success':
            result = item.get('result', {})
            review_res = result.get('review_res', {})
            main_pipe_map[case_id] = extract_categories_from_result(review_res)
    
    # 对比分析
    categories = ["重大客诉", "索要联系方式", "兜售用户信息", "负面新闻", "辱骂信息", "咨询公司信息"]
    
    print(f"\\n📈 方法特点对比:")
    print("-" * 50)
    print("MainPipe方法:")
    print("  - 基于规则匹配和向量搜索")
    print("  - 使用预定义的关键词库")
    print("  - 支持复杂的匹配逻辑")
    
    print("\\nLangExtract方法:")
    print("  - 基于大语言模型的文本提取")
    print("  - 更注重语义理解")
    print("  - 能提取精确的文本片段")
    
    print(f"\\n📊 检测结果对比 (示例案例):")
    print("-" * 60)
    print(f"{'类别':<15} {'MainPipe':<10} {'LangExtract':<12} {'一致性'}")
    print("-" * 60)
    
    total_agreement = 0
    total_comparisons = 0
    
    for lx_result in langextract_results:
        case_id = lx_result['case_id']
        lx_labels = extract_categories_from_result(lx_result['result']['review_res'])
        mp_labels = main_pipe_map.get(case_id, {category: 0 for category in categories})
        
        print(f"\\n案例 {case_id}:")
        case_agreement = 0
        
        for category in categories:
            lx_hit = lx_labels.get(category, 0)
            mp_hit = mp_labels.get(category, 0)
            agreement = "✅" if lx_hit == mp_hit else "❌"
            
            if lx_hit == mp_hit:
                case_agreement += 1
            
            print(f"{category:<15} {mp_hit:<10} {lx_hit:<12} {agreement}")
        
        total_agreement += case_agreement
        total_comparisons += len(categories)
        
        print(f"案例一致性: {case_agreement}/{len(categories)} ({case_agreement/len(categories)*100:.1f}%)")
    
    overall_agreement = total_agreement / total_comparisons if total_comparisons > 0 else 0
    
    print(f"\\n🎯 总体分析:")
    print("-" * 30)
    print(f"总体一致性: {overall_agreement*100:.1f}%")
    
    # 方法优劣分析
    print(f"\\n💡 方法优劣分析:")
    print("-" * 30)
    print("MainPipe优势:")
    print("  ✅ 处理速度快")
    print("  ✅ 规则可控性强") 
    print("  ✅ 成本较低")
    
    print("\\nLangExtract优势:")
    print("  ✅ 语义理解能力强")
    print("  ✅ 能处理复杂语境")
    print("  ✅ 提取结果更精确")
    
    print("\\nMainPipe劣势:")
    print("  ❌ 可能遗漏语义相似但词汇不同的内容")
    print("  ❌ 规则维护复杂")
    
    print("\\nLangExtract劣势:")
    print("  ❌ 处理速度较慢")
    print("  ❌ API调用成本高")
    print("  ❌ 结果一致性可能有波动")
    
    # 保存对比结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    comparison_result = {
        "timestamp": timestamp,
        "method_comparison": {
            "main_pipe": "基于规则匹配和向量搜索",
            "langextract": "基于大语言模型文本提取"
        },
        "sample_cases": len(langextract_results),
        "overall_agreement": overall_agreement,
        "recommendations": {
            "production_use": "建议使用MainPipe作为主要方法，LangExtract作为验证和优化工具",
            "accuracy_improvement": "可以用LangExtract的结果来优化MainPipe的规则",
            "hybrid_approach": "考虑混合方法：MainPipe做初筛，LangExtract做精确验证"
        }
    }
    
    output_file = f"results/method_comparison_demo_{timestamp}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_result, f, ensure_ascii=False, indent=2)
    
    print(f"\\n✅ 对比结果已保存到: {output_file}")

if __name__ == "__main__":
    compare_methods()
