#!/usr/bin/env python3
"""
简化版：提取六个关键类别的检测结果
"""

import json
import csv
from datetime import datetime

# 目标六个类别
TARGET_CATEGORIES = [
    "重大客诉",
    "索要联系方式", 
    "兜售用户信息",
    "负面新闻",
    "辱骂信息",
    "咨询公司信息"
]

def main():
    # 读取结果文件
    print("🔍 读取模型结果文件...")
    with open('results/individual_cases_results_20250811_104748.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 总案例数: {len(data)}")
    
    # 提取结果
    results = []
    category_stats = {category: 0 for category in TARGET_CATEGORIES}
    
    for item in data:
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        # 初始化该案例的标签
        case_labels = {category: 0 for category in TARGET_CATEGORIES}
        
        # 检查 sensitive_inquiry
        for item_data in review_res.get('sensitive_inquiry', []):
            if item_data.get('hit_rule', False):
                category = item_data.get('type', '')
                if category in TARGET_CATEGORIES:
                    case_labels[category] = 1
                    category_stats[category] += 1
        
        # 检查 sensitive_reply
        for item_data in review_res.get('sensitive_reply', []):
            if item_data.get('hit_rule', False):
                category = item_data.get('type', '')
                if category in TARGET_CATEGORIES:
                    case_labels[category] = 1
                    category_stats[category] += 1
        
        # 构建结果记录
        result_record = {'case_id': case_id}
        for category in TARGET_CATEGORIES:
            result_record[category] = case_labels[category]
        
        results.append(result_record)
    
    # 保存CSV文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"results/six_categories_results_{timestamp}.csv"
    
    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['case_id'] + TARGET_CATEGORIES
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)
    
    # 保存统计报告
    report_filename = f"results/six_categories_report_{timestamp}.txt"
    
    total_cases = len(results)
    cases_with_any_hit = len([r for r in results if any(r[cat] == 1 for cat in TARGET_CATEGORIES)])
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("六个关键类别检测结果报告\\n")
        f.write("=" * 50 + "\\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n")
        f.write(f"总案例数: {total_cases}\\n")
        f.write(f"有风险检测的案例数: {cases_with_any_hit} ({cases_with_any_hit/total_cases*100:.1f}%)\\n\\n")
        
        f.write("各类别检测统计:\\n")
        f.write("-" * 30 + "\\n")
        
        for category in TARGET_CATEGORIES:
            count = category_stats[category]
            percentage = (count / total_cases) * 100
            f.write(f"{category:<15}: {count:>4} 案例 ({percentage:>5.1f}%)\\n")
        
        f.write(f"\\n总检测次数: {sum(category_stats.values())}\\n")
        f.write(f"平均每案例检测: {sum(category_stats.values())/total_cases:.2f}\\n")
        
        # 排序显示
        f.write(f"\\n检测频率排序:\\n")
        sorted_categories = sorted(category_stats.items(), key=lambda x: x[1], reverse=True)
        for i, (category, count) in enumerate(sorted_categories, 1):
            f.write(f"{i}. {category}: {count} 次\\n")
    
    # 打印结果
    print(f"\\n📈 六个类别检测统计:")
    print("-" * 50)
    
    for category in TARGET_CATEGORIES:
        count = category_stats[category]
        percentage = (count / total_cases) * 100
        print(f"{category:<15}: {count:>4} 案例 ({percentage:>5.1f}%)")
    
    total_hits = sum(category_stats.values())
    
    print(f"\\n🎯 总体统计:")
    print("-" * 30)
    print(f"有风险检测的案例: {cases_with_any_hit} ({cases_with_any_hit/total_cases*100:.1f}%)")
    print(f"总检测次数: {total_hits}")
    print(f"平均每案例检测: {total_hits/total_cases:.2f}")
    
    print(f"\\n✅ 文件已保存:")
    print(f"📊 CSV数据: {csv_filename}")
    print(f"📋 统计报告: {report_filename}")

if __name__ == "__main__":
    main()
