#!/usr/bin/env python3
"""
使用新模型批量处理案例
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime
import traceback

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


def load_cases(data_dir="data/individual_cases", limit=50):
    """加载案例数据"""
    data_path = Path(data_dir)
    if not data_path.exists():
        print(f"❌ 数据目录不存在: {data_path}")
        return []

    cases = []
    json_files = list(data_path.glob("case_*.json"))

    print(f"找到 {len(json_files)} 个案例文件，处理前 {limit} 个")

    for json_file in sorted(json_files)[:limit]:
        try:
            with open(json_file, "r", encoding="utf-8") as f:
                case_data = json.load(f)
                cases.append(case_data)
        except Exception as e:
            print(f"读取文件 {json_file} 时出错: {e}")
            continue

    return cases


def run_pipeline_on_case(pipeline, case_data):
    """在单个案例上运行pipeline"""
    case_id = case_data.get(
        "caseId", case_data.get("case_id", "unknown")
    )  # 支持两种格式
    messages = case_data.get("messages", [])

    try:
        result = pipeline.run(messages, case_id)  # 修正参数顺序
        return {
            "case_id": case_id,
            "status": "success",
            "result": result,
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }
    except Exception as e:
        return {
            "case_id": case_id,
            "status": "error",
            "error": str(e),
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }


def extract_categories_from_result(review_res):
    """从review_res中提取六个类别的标签"""
    categories = [
        "重大客诉",
        "索要联系方式",
        "兜售用户信息",
        "负面新闻",
        "辱骂信息",
        "咨询公司信息",
    ]
    labels = {category: 0 for category in categories}

    # 检查 sensitive_inquiry
    for item_data in review_res.get("sensitive_inquiry", []):
        if item_data.get("hit_rule", False):
            category = item_data.get("type", "")
            if category in categories:
                labels[category] = 1

    # 检查 sensitive_reply
    for item_data in review_res.get("sensitive_reply", []):
        if item_data.get("hit_rule", False):
            category = item_data.get("type", "")
            if category in categories:
                labels[category] = 1

    return labels


def main():
    """主函数"""
    print("🚀 新模型批量测试")
    print("=" * 50)

    # 导入pipeline
    try:
        from dc_ai_red_line_review.main_pipe import BasicPipeline

        print("✅ 成功导入BasicPipeline")
    except Exception as e:
        print(f"❌ 导入BasicPipeline失败: {e}")
        return

    # 加载案例
    cases = load_cases(limit=50)  # 处理50个案例
    if not cases:
        print("❌ 没有找到案例数据")
        return

    # 初始化pipeline
    try:
        pipeline = BasicPipeline()
        print("✅ 成功初始化pipeline")
    except Exception as e:
        print(f"❌ 初始化pipeline失败: {e}")
        return

    # 批量处理
    results = []
    success_count = 0
    error_count = 0

    categories = [
        "重大客诉",
        "索要联系方式",
        "兜售用户信息",
        "负面新闻",
        "辱骂信息",
        "咨询公司信息",
    ]
    category_stats = {cat: 0 for cat in categories}

    print(f"\n📊 开始处理 {len(cases)} 个案例...")

    for i, case in enumerate(cases):
        case_id = case.get(
            "caseId", case.get("case_id", f"unknown_{i}")
        )  # 支持两种格式
        print(f"处理案例 {i + 1}/{len(cases)}: {case_id}")

        try:
            result = run_pipeline_on_case(pipeline, case)
            results.append(result)

            if result.get("status") == "success":
                success_count += 1

                # 统计检测结果
                review_res = result.get("result", {}).get("review_res", {})
                labels = extract_categories_from_result(review_res)

                hits = [cat for cat, val in labels.items() if val == 1]
                if hits:
                    print(f"  ✅ 检测到: {', '.join(hits)}")
                    for cat in hits:
                        category_stats[cat] += 1
                else:
                    print(f"  ⚪ 无风险检测")
            else:
                error_count += 1
                print(f"  ❌ 处理失败: {result.get('error', 'Unknown error')}")

        except Exception as e:
            error_count += 1
            print(f"  ❌ 处理异常: {e}")
            results.append(
                {
                    "case_id": case_id,
                    "status": "error",
                    "error": str(e),
                    "processed_at": datetime.now().isoformat(),
                }
            )

    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results/new_model_batch_results_{timestamp}.json"

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    # 统计报告
    print(f"\n📊 新模型批量测试结果:")
    print("=" * 50)
    print(f"总案例数: {len(cases)}")
    print(f"成功处理: {success_count}")
    print(f"处理失败: {error_count}")
    print(f"成功率: {success_count / len(cases) * 100:.1f}%")

    total_hits = sum(category_stats.values())
    cases_with_hits = len(
        [
            r
            for r in results
            if r.get("status") == "success"
            and any(
                extract_categories_from_result(
                    r.get("result", {}).get("review_res", {})
                ).values()
            )
        ]
    )

    print(f"\n📈 检测统计:")
    print("-" * 30)
    if success_count > 0:
        print(
            f"有风险检测的案例: {cases_with_hits} ({cases_with_hits / success_count * 100:.1f}%)"
        )
        print(f"总检测次数: {total_hits}")
        print(f"平均每案例检测: {total_hits / success_count:.2f}")
    else:
        print("❌ 没有成功处理的案例")

    print(f"\n各类别检测统计:")
    print("-" * 30)
    if success_count > 0:
        for category, count in category_stats.items():
            percentage = (count / success_count) * 100 if success_count > 0 else 0
            print(f"{category:<15}: {count:>3} 次 ({percentage:>5.1f}%)")
    else:
        print("无统计数据")

    print(f"\n✅ 结果已保存到: {output_file}")

    # 创建简化的CSV统计
    csv_file = f"results/new_model_stats_{timestamp}.csv"
    with open(csv_file, "w", encoding="utf-8") as f:
        f.write("类别,命中次数,占比,说明\n")
        for category, count in category_stats.items():
            percentage = (count / success_count) * 100 if success_count > 0 else 0
            f.write(f"{category},{count},{percentage:.1f}%,新模型检测结果\n")

    print(f"📊 CSV统计已保存到: {csv_file}")

    return {
        "total_cases": len(cases),
        "success_count": success_count,
        "error_count": error_count,
        "cases_with_hits": cases_with_hits,
        "total_hits": total_hits,
        "category_stats": category_stats,
        "output_file": output_file,
    }


if __name__ == "__main__":
    try:
        stats = main()
        print(f"\n🎯 测试完成！")
        if stats:
            print(
                f"新模型在 {stats['success_count']} 个案例中检测到 {stats['total_hits']} 次风险"
            )
            print(
                f"检测覆盖率: {stats['cases_with_hits']}/{stats['success_count']} = {stats['cases_with_hits'] / stats['success_count'] * 100:.1f}%"
            )
    except Exception as e:
        print(f"❌ 运行过程中出错: {e}")
        traceback.print_exc()
