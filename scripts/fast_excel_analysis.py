#!/usr/bin/env python3
"""
快速分析Excel文件结构，专注于找到人工标注列
"""

import pandas as pd
import json
from pathlib import Path

def analyze_single_file(filepath, max_rows=50):
    """分析单个Excel文件，只读取少量行来快速获取结构信息"""
    print(f"\n分析文件: {filepath.name}")
    print("-" * 50)
    
    try:
        # 先读取列名（只读第一行）
        df_header = pd.read_excel(filepath, nrows=0)
        columns = list(df_header.columns)
        print(f"总列数: {len(columns)}")
        
        # 查找可能的标注列
        annotation_keywords = ['正确', '审核', '标注', '结果', 'result', 'label', 'annotation', '人工', '标记', '答案']
        potential_annot_cols = []
        
        for col in columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in annotation_keywords):
                potential_annot_cols.append(col)
        
        print(f"可能的标注列: {potential_annot_cols}")
        
        if potential_annot_cols:
            # 只读取标注列和基础列
            basic_cols = ['f_case_id', 'f_msg_id', 'f_user_type', 'f_original_msg']
            cols_to_read = []
            for col in basic_cols:
                if col in columns:
                    cols_to_read.append(col)
            cols_to_read.extend(potential_annot_cols)
            
            # 读取少量数据来分析
            df = pd.read_excel(filepath, usecols=cols_to_read, nrows=max_rows)
            
            print(f"\n标注列分析 (基于前{max_rows}行):")
            for col in potential_annot_cols:
                if col in df.columns:
                    non_null_count = df[col].notna().sum()
                    print(f"\n列: {col}")
                    print(f"  非空值: {non_null_count}/{len(df)}")
                    
                    if non_null_count > 0:
                        # 获取第一个非空值
                        first_non_null = df[col].dropna().iloc[0]
                        print(f"  数据类型: {type(first_non_null)}")
                        
                        val_str = str(first_non_null)
                        if len(val_str) > 100:
                            print(f"  样例值: {val_str[:100]}...")
                        else:
                            print(f"  样例值: {val_str}")
                        
                        # 检查是否是JSON
                        if val_str.strip().startswith('{') or val_str.strip().startswith('['):
                            try:
                                parsed = json.loads(val_str)
                                print(f"  JSON解析: 成功")
                                if isinstance(parsed, dict):
                                    print(f"  JSON键: {list(parsed.keys())[:5]}")
                            except:
                                print(f"  JSON解析: 失败")
                        
                        # 检查唯一值
                        unique_vals = df[col].dropna().unique()
                        if len(unique_vals) <= 10:
                            print(f"  所有唯一值: {list(unique_vals)}")
                        else:
                            print(f"  唯一值数量: {len(unique_vals)}")
        
        # 检查是否有小类别相关的列
        small_classes = ["政府机构", "政府邮箱", "key_contact", "internal_system", 
                        "咨询公司信息", "兜售用户信息", "负面新闻", "重大客诉", 
                        "索要联系方式", "辱骂信息"]
        
        class_cols = []
        for col in columns:
            if any(cls in str(col) for cls in small_classes):
                class_cols.append(col)
        
        if class_cols:
            print(f"\n包含小类别名称的列: {class_cols}")
            # 读取这些列的数据
            df_class = pd.read_excel(filepath, usecols=class_cols, nrows=max_rows)
            for col in class_cols:
                unique_vals = df_class[col].dropna().unique()
                print(f"  {col}: {list(unique_vals)[:5]} (共{len(unique_vals)}个唯一值)")
        
        return potential_annot_cols, class_cols
        
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return [], []

def main():
    """主函数"""
    gt_dir = Path("data/gt")
    excel_files = sorted(gt_dir.glob("gt_*.xlsx"))
    
    if not excel_files:
        print("未找到gt_*.xlsx文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件")
    
    all_annot_cols = set()
    all_class_cols = set()
    
    # 只分析第一个文件来快速获取结构
    if excel_files:
        annot_cols, class_cols = analyze_single_file(excel_files[0], max_rows=20)
        all_annot_cols.update(annot_cols)
        all_class_cols.update(class_cols)
    
    print(f"\n{'='*60}")
    print("总结:")
    print(f"发现的标注列: {list(all_annot_cols)}")
    print(f"发现的小类别列: {list(all_class_cols)}")
    
    # 给出建议
    if all_annot_cols:
        print(f"\n建议: 修改build_ground_truth.py中的parse_annot_cols函数")
        print(f"添加对以下列的支持: {list(all_annot_cols)}")
    elif all_class_cols:
        print(f"\n建议: 数据可能使用分列格式，每个小类别一列")
        print(f"需要修改解析逻辑来处理这种格式")
    else:
        print(f"\n警告: 未找到明显的标注列，需要人工确认列名")

if __name__ == "__main__":
    main()
