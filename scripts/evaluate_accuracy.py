#!/usr/bin/env python3
"""
准确率评估工具 - 对比模型输出结果与ground truth数据
"""

import argparse
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict
import numpy as np
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

# 小类别列表（与build_ground_truth.py保持一致）
SMALL_CLASSES = [
    "政府机构",
    "政府邮箱", 
    "key_contact",
    "internal_system",
    "咨询公司信息",
    "兜售用户信息",
    "负面新闻",
    "重大客诉",
    "索要联系方式",
    "辱骂信息",
]

def load_ground_truth(gt_path: str) -> Dict[int, Dict[str, int]]:
    """加载ground truth数据"""
    gt_labels = {}
    
    if gt_path.endswith('.jsonl'):
        # 从JSONL文件加载
        with open(gt_path, 'r', encoding='utf-8') as f:
            for line in f:
                data = json.loads(line.strip())
                case_id = data.get('case_id')
                labels = data.get('labels')
                if case_id and labels:
                    gt_labels[case_id] = labels
    
    elif gt_path.endswith('.csv'):
        # 从CSV文件加载
        df = pd.read_csv(gt_path)
        for _, row in df.iterrows():
            case_id = row['case_id']
            labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            gt_labels[case_id] = labels
    
    else:
        raise ValueError(f"不支持的文件格式: {gt_path}")
    
    print(f"加载了 {len(gt_labels)} 个案例的ground truth标签")
    return gt_labels

def load_model_results(results_path: str) -> Dict[int, Dict[str, int]]:
    """加载模型输出结果"""
    model_labels = {}
    
    if results_path.endswith('.json'):
        # 从JSON文件加载
        with open(results_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        for case_data in data:
            case_id = case_data.get('case_id')
            review_res = case_data.get('review_res', {})
            
            if case_id:
                # 将review_res转换为小类别标签
                labels = review_res_to_labels(review_res)
                model_labels[case_id] = labels
    
    elif results_path.endswith('.csv'):
        # 从CSV文件加载
        df = pd.read_csv(results_path)
        for _, row in df.iterrows():
            case_id = row['case_id']
            
            # 检查是否有review_res列
            if 'review_res' in row and pd.notna(row['review_res']):
                try:
                    review_res = json.loads(row['review_res'])
                    labels = review_res_to_labels(review_res)
                except:
                    # 如果JSON解析失败，尝试直接读取小类别列
                    labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            else:
                # 直接从小类别列读取
                labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            
            model_labels[case_id] = labels
    
    print(f"加载了 {len(model_labels)} 个案例的模型结果")
    return model_labels

def review_res_to_labels(review_res: Dict[str, Any]) -> Dict[str, int]:
    """将review_res转换为小类别标签（与build_ground_truth.py保持一致）"""
    labels = {k: 0 for k in SMALL_CLASSES}

    # government_inquiry
    gov = review_res.get("government_inquiry")
    if isinstance(gov, list):
        for item in gov:
            t = item.get("type")
            if t in ("政府机构", "政府邮箱"):
                labels[t] = 1 if (item.get("hit_rule") and (item.get("values") or [])) else 0

    # key_contact & internal_system
    for key, cname in [("key_contact", "key_contact"), ("internal_system", "internal_system")]:
        obj = review_res.get(key)
        if isinstance(obj, dict):
            labels[cname] = 1 if (obj.get("hit_rule") and (obj.get("values") or [])) else 0

    # sensitive_inquiry
    si = review_res.get("sensitive_inquiry")
    if isinstance(si, list):
        for item in si:
            t = item.get("type")
            if t in ("咨询公司信息", "兜售用户信息", "负面新闻", "重大客诉"):
                labels[t] = 1 if (item.get("hit_rule") and (item.get("values") or [])) else 0

    # sensitive_reply
    sr = review_res.get("sensitive_reply")
    if isinstance(sr, list):
        for item in sr:
            t = item.get("type")
            if t in ("索要联系方式", "辱骂信息"):
                labels[t] = 1 if (item.get("hit_rule") and (item.get("values") or [])) else 0

    return labels

def calculate_metrics(gt_labels: Dict[int, Dict[str, int]], 
                     model_labels: Dict[int, Dict[str, int]]) -> Dict[str, Any]:
    """计算各种评估指标"""
    
    # 找到共同的案例ID
    common_case_ids = set(gt_labels.keys()) & set(model_labels.keys())
    print(f"共同案例数: {len(common_case_ids)}")
    
    if not common_case_ids:
        raise ValueError("没有找到共同的案例ID")
    
    # 准备数据
    metrics = {}
    
    # 整体指标
    all_gt = []
    all_pred = []
    
    # 按类别的指标
    class_metrics = {}
    
    for cls in SMALL_CLASSES:
        gt_cls = [gt_labels[cid][cls] for cid in common_case_ids]
        pred_cls = [model_labels[cid][cls] for cid in common_case_ids]
        
        all_gt.extend(gt_cls)
        all_pred.extend(pred_cls)
        
        # 计算该类别的指标
        if sum(gt_cls) > 0 or sum(pred_cls) > 0:  # 只有当该类别有正例时才计算
            precision, recall, f1, support = precision_recall_fscore_support(
                gt_cls, pred_cls, average='binary', zero_division=0
            )
            
            # 混淆矩阵
            tn, fp, fn, tp = confusion_matrix(gt_cls, pred_cls).ravel()
            
            class_metrics[cls] = {
                'precision': float(precision),
                'recall': float(recall),
                'f1': float(f1),
                'support': int(support),
                'tp': int(tp),
                'tn': int(tn),
                'fp': int(fp),
                'fn': int(fn),
                'accuracy': float((tp + tn) / (tp + tn + fp + fn)) if (tp + tn + fp + fn) > 0 else 0.0
            }
        else:
            class_metrics[cls] = {
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0,
                'support': 0,
                'tp': 0,
                'tn': len(gt_cls),
                'fp': 0,
                'fn': 0,
                'accuracy': 1.0
            }
    
    # 整体微平均指标
    overall_precision, overall_recall, overall_f1, _ = precision_recall_fscore_support(
        all_gt, all_pred, average='micro', zero_division=0
    )
    
    # 整体宏平均指标
    macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
        all_gt, all_pred, average='macro', zero_division=0
    )
    
    metrics['overall'] = {
        'micro_precision': float(overall_precision),
        'micro_recall': float(overall_recall),
        'micro_f1': float(overall_f1),
        'macro_precision': float(macro_precision),
        'macro_recall': float(macro_recall),
        'macro_f1': float(macro_f1),
        'total_cases': len(common_case_ids)
    }
    
    metrics['by_class'] = class_metrics
    
    return metrics

def print_metrics_report(metrics: Dict[str, Any]):
    """打印评估报告"""
    print("\n" + "="*80)
    print("准确率评估报告")
    print("="*80)
    
    # 整体指标
    overall = metrics['overall']
    print(f"\n整体指标 (基于 {overall['total_cases']} 个案例):")
    print(f"  微平均 - Precision: {overall['micro_precision']:.4f}, Recall: {overall['micro_recall']:.4f}, F1: {overall['micro_f1']:.4f}")
    print(f"  宏平均 - Precision: {overall['macro_precision']:.4f}, Recall: {overall['macro_recall']:.4f}, F1: {overall['macro_f1']:.4f}")
    
    # 按类别的详细指标
    print(f"\n按类别详细指标:")
    print(f"{'类别':<15} {'Precision':<10} {'Recall':<10} {'F1':<10} {'Accuracy':<10} {'Support':<8} {'TP':<4} {'FP':<4} {'FN':<4} {'TN':<4}")
    print("-" * 100)
    
    for cls in SMALL_CLASSES:
        m = metrics['by_class'][cls]
        print(f"{cls:<15} {m['precision']:<10.4f} {m['recall']:<10.4f} {m['f1']:<10.4f} {m['accuracy']:<10.4f} {m['support']:<8} {m['tp']:<4} {m['fp']:<4} {m['fn']:<4} {m['tn']:<4}")

def save_detailed_results(metrics: Dict[str, Any], output_path: str):
    """保存详细结果到文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=2)
    print(f"\n详细结果已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="评估模型输出结果的准确率")
    parser.add_argument("--gt", required=True, help="Ground truth文件路径 (.jsonl 或 .csv)")
    parser.add_argument("--pred", required=True, help="模型预测结果文件路径 (.json 或 .csv)")
    parser.add_argument("--output", help="输出详细结果的文件路径 (可选)")
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        print("加载ground truth数据...")
        gt_labels = load_ground_truth(args.gt)
        
        print("加载模型预测结果...")
        model_labels = load_model_results(args.pred)
        
        # 计算指标
        print("计算评估指标...")
        metrics = calculate_metrics(gt_labels, model_labels)
        
        # 打印报告
        print_metrics_report(metrics)
        
        # 保存详细结果
        if args.output:
            save_detailed_results(metrics, args.output)
        
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
