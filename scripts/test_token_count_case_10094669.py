#!/usr/bin/env python3
"""
精准测试单条案例的 token 数（通过真实 API 请求获取 usage）

- 读取 data/individual_cases/case_<CASE_ID>.json
- 使用与流水线一致的拼接格式：<|im_start|>{type}\n{msg}<|im_end|>，按行拼接
- 使用与流水线一致的 system 提示词（基于 core.unified_all_prompt + 额外约束）
- 调用两次 API：
  1) 仅 system（得到 system_prompt_tokens）
  2) system + content（得到 prompt_tokens_total）
  内容 token = prompt_tokens_total - system_prompt_tokens
- 另打印 utils.get_token_count 的本地估算值作参考

运行示例：
  uv run python scripts/test_token_count_case_10094669.py --case-id 10094669

需要环境变量：QWQ_BASE_URL, QWQ_API_KEY, MODEL_NAME
"""

# 10203334,10094669

from __future__ import annotations

import argparse
import json
import os
from pathlib import Path

import httpx
from dotenv import load_dotenv
from openai import OpenAI

from dc_ai_red_line_review.core import unified_all_prompt
from dc_ai_red_line_review.utils import get_logger, get_token_count


def load_case_messages(case_id: str) -> list[dict]:
    case_path = Path("data/individual_cases") / f"case_{case_id}.json"
    if not case_path.exists():
        raise FileNotFoundError(f"案例文件不存在: {case_path}")
    with case_path.open(encoding="utf-8") as f:
        data = json.load(f)
    msgs = data.get("messages", [])
    if not isinstance(msgs, list) or not msgs:
        raise ValueError(f"案例 {case_id} 没有 messages 或格式不正确")
    return msgs


def build_content_like_pipeline(messages: list[dict]) -> str:
    """按 RetrievalCore._get_splitter_res 的方式拼接 content。"""
    parts = [f"<|im_start|>{m['type']}\n{m['msg']}<|im_end|>" for m in messages]
    return "\n".join(parts)


def build_enhanced_system_prompt() -> str:
    """复用 core.py 的系统提示词拼接逻辑。"""
    extra = (
        "\n\n🚨 CRITICAL EXTRACTION REQUIREMENTS 🚨\n"
        "1. VALUES MUST BE VERBATIM: Copy text EXACTLY as it appears in the conversation\n"
        "2. NO MODIFICATIONS: Do not change, summarize, paraphrase, or translate any text\n"
        "3. PRESERVE ORIGINAL LANGUAGE: Keep the exact language of the source text (Chinese stays Chinese, English stays English)\n"
        "4. NO RULE TEXT: Do not copy text from rule descriptions or examples - ONLY from the conversation\n"
        "5. NO INFERENCE: Do not generate content that doesn't exist in the conversation\n"
        "6. EMPTY IF NO MATCH: Return [] if no exact matches are found in the conversation\n"
        "7. SOURCE VERIFICATION: Every value must be traceable to specific text in the conversation\n"
        "8. CHARACTER-LEVEL ACCURACY: Preserve exact characters, punctuation, spacing, and formatting\n"
        "9. IGNORE EXAMPLES: All example text in the rules above should be ignored - only analyze the actual conversation\n"
        "10. CONVERSATION ONLY: Extract ONLY from the conversation content between <|im_start|> and <|im_end|> tags\n"
        "\nREMEMBER: You are extracting evidence from the conversation, not interpreting rules or creating content. NEVER translate or modify the original language."
    )
    return unified_all_prompt + extra


def create_client() -> OpenAI:
    base_url = os.environ["QWQ_BASE_URL"]
    api_key = os.environ["QWQ_API_KEY"]
    client = OpenAI(
        base_url=base_url,
        api_key=api_key,
        http_client=httpx.Client(verify=False, timeout=180.0),  # 延长超时，防止大输入耗时
    )
    return client


def call_and_get_prompt_tokens(client: OpenAI, messages: list[dict]) -> int:
    """调用一次 API，返回 usage.prompt_tokens（若无则抛错）。"""
    model = os.environ["MODEL_NAME"]
    try:
        resp = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.0,
            top_p=0.0,
            max_tokens=1,  # 最小化响应 token 开销
            extra_body={"chat_template_kwargs": {"enable_thinking": False}},
        )
    except Exception as e:
        raise RuntimeError(f"API 调用失败: {e}")

    usage = getattr(resp, "usage", None)
    if usage is None or getattr(usage, "prompt_tokens", None) is None:
        raise RuntimeError(f"响应未包含 usage.prompt_tokens，无法精准统计，原始响应: {resp}")
    return int(usage.prompt_tokens)


def main():
    load_dotenv(override=True)
    logger = get_logger(module_name="test_token_count")

    parser = argparse.ArgumentParser(description="精准测试单条案例 token 数（API 实测）")
    parser.add_argument("--case-id", default="10094669", help="案例ID，默认 10094669")
    args = parser.parse_args()

    case_id = args.case_id

    # 读取与拼接
    messages = load_case_messages(case_id)
    content = build_content_like_pipeline(messages)
    sys_prompt = build_enhanced_system_prompt()

    # 本地估算（参考）
    est_content_tokens = get_token_count(content)
    content_chars = len(content)

    logger.info(f"Case {case_id}: messages={len(messages)}, content_chars={content_chars}")
    logger.info(f"Local estimated content tokens (utils.get_token_count): ~{est_content_tokens}")

    # 创建客户端
    client = create_client()

    # 仅 system 提示词
    system_only_msgs = [{"role": "system", "content": sys_prompt}]
    sys_only_prompt_tokens = call_and_get_prompt_tokens(client, system_only_msgs)

    # system + content
    full_msgs = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": content},
    ]
    full_prompt_tokens = call_and_get_prompt_tokens(client, full_msgs)

    # 计算内容 token（API 精准）
    content_tokens_api = full_prompt_tokens - sys_only_prompt_tokens

    print("\n===== Token 统计（API 实测）=====")
    print(f"Case ID: {case_id}")
    print(f"消息条数: {len(messages)}")
    print(f"内容字符数: {content_chars}")
    print(f"System-only prompt_tokens: {sys_only_prompt_tokens}")
    print(f"System+Content prompt_tokens: {full_prompt_tokens}")
    print(f"内容 tokens（API 计算）: {content_tokens_api}")
    print(f"内容 tokens（本地估算）: ~{est_content_tokens}")
    if est_content_tokens:
        ratio = content_tokens_api / est_content_tokens
        print(f"估算/实测 比例: {ratio:.3f}（>1 表示估算偏小，<1 表示估算偏大）")
    print("================================\n")


if __name__ == "__main__":
    main()

