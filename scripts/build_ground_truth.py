import argparse
import json
import os
import re
from collections import defaultdict
from dataclasses import dataclass, asdict
from datetime import datetime
from html import unescape as html_unescape
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd

# Small-class keys used for 0/1 labels (Chinese names kept to align with existing outputs)
SMALL_CLASSES = [
    "政府机构",
    "政府邮箱",
    "key_contact",
    "internal_system",
    "咨询公司信息",
    "兜售用户信息",
    "负面新闻",
    "重大客诉",
    "索要联系方式",
    "辱骂信息",
]


@dataclass
class Message:
    id: int
    type: str
    msg: str


def strip_html(raw: str) -> str:
    """Basic HTML to text. Avoid external deps; handle <br> and entities.
    - Convert <br/>, </p> to newlines
    - Remove tags
    - Unescape HTML entities
    - Normalize whitespace
    """
    if not isinstance(raw, str):
        return ""
    text = raw
    # common line breaks
    text = re.sub(r"<\s*br\s*/?>", "\n", text, flags=re.IGNORECASE)
    text = re.sub(r"</\s*p\s*>", "\n", text, flags=re.IGNORECASE)
    # remove tags
    text = re.sub(r"<[^>]+>", " ", text)
    # unescape entities
    text = html_unescape(text)
    # collapse whitespace
    text = re.sub(r"[\t\r ]+", " ", text)
    # normalize newlines and spaces
    text = re.sub(r"\s*\n\s*", "\n", text)
    text = text.strip()
    return text


def parse_f_original_msg(cell: Any) -> str:
    """Parse f_original_msg which may be:
    - a JSON string with {"content": "<html>..."}
    - a plain HTML string
    - already plain text
    Returns cleaned plain text.
    """
    if cell is None or (isinstance(cell, float) and pd.isna(cell)):
        return ""
    s = str(cell)
    s = s.strip()
    # try json
    if s.startswith("{") and '"content"' in s:
        try:
            d = json.loads(s)
            content = d.get("content", "")
            return strip_html(content)
        except Exception:
            # fall back to HTML stripping of the raw string
            return strip_html(s)
    # looks like HTML
    if "<" in s and ">" in s:
        return strip_html(s)
    # plain text
    return s


def normalize_role(v: Any) -> str:
    s = str(v or "").strip().upper()
    if s in {"USER", "AGENT"}:
        return s
    # heuristic mapping
    if s in {"U", "CUSTOMER", "客户", "用户"}:
        return "USER"
    if s in {"A", "CS", "AGENT", "客服"}:
        return "AGENT"
    return "USER"


def to_int(v: Any, default: int = 0) -> int:
    try:
        if pd.isna(v):
            return default
    except Exception:
        pass
    try:
        return int(v)
    except Exception:
        return default


def review_res_to_smallclass_labels(review_res: Dict[str, Any]) -> Dict[str, int]:
    """Convert review_res dict into 0/1 labels per small class.
    Expects structure similar to pipeline outputs.
    """
    labels = {k: 0 for k in SMALL_CLASSES}

    # government_inquiry: list of dicts with type + hit_rule + values
    gov = review_res.get("government_inquiry")
    if isinstance(gov, list):
        for item in gov:
            t = item.get("type")
            if t in ("政府机构", "政府邮箱"):
                labels[t] = (
                    1 if (item.get("hit_rule") and (item.get("values") or [])) else 0
                )

    # key_contact & internal_system: dicts with hit_rule + values
    for key, cname in [
        ("key_contact", "key_contact"),
        ("internal_system", "internal_system"),
    ]:
        obj = review_res.get(key)
        if isinstance(obj, dict):
            labels[cname] = (
                1 if (obj.get("hit_rule") and (obj.get("values") or [])) else 0
            )

    # sensitive_inquiry list
    si = review_res.get("sensitive_inquiry")
    if isinstance(si, list):
        for item in si:
            t = item.get("type")
            if t in ("咨询公司信息", "兜售用户信息", "负面新闻", "重大客诉"):
                labels[t] = (
                    1 if (item.get("hit_rule") and (item.get("values") or [])) else 0
                )

    # sensitive_reply list
    sr = review_res.get("sensitive_reply")
    if isinstance(sr, list):
        for item in sr:
            t = item.get("type")
            if t in ("索要联系方式", "辱骂信息"):
                labels[t] = (
                    1 if (item.get("hit_rule") and (item.get("values") or [])) else 0
                )

    return labels


def parse_annot_cols(
    row: pd.Series,
) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, int]]]:
    """Try to parse human annotation columns into review_res and labels.
    Returns (review_res_gt, labels) where either may be None if unavailable.
    We attempt the following sources (in order):
    - 正确结果, 人工审核结果: try JSON first; if contains review_res or already a primitive 0/1 per class
    - Any column containing '标注', '审核', '结果', 'result', 'label', 'annotation'
    - Individual small class columns (if they exist as separate columns)
    """
    # Extended list of possible annotation column names
    annotation_col_patterns = [
        "正确结果",
        "人工审核结果",
        "标注结果",
        "审核结果",
        "人工标注",
        "标注",
        "审核",
        "结果",
        "答案",
        "ground_truth",
        "gt",
        "result",
        "label",
        "annotation",
        "manual_review",
    ]

    # First try exact matches and pattern matches
    for pattern in annotation_col_patterns:
        # Exact match
        if pattern in row and pd.notna(row[pattern]):
            result = _parse_single_annotation_cell(row[pattern])
            if result[0] is not None or result[1] is not None:
                return result

        # Partial match (case insensitive)
        for col in row.index:
            if pattern.lower() in str(col).lower() and pd.notna(row[col]):
                result = _parse_single_annotation_cell(row[col])
                if result[0] is not None or result[1] is not None:
                    return result

    # Try to parse individual small class columns
    labels = {}
    found_any_label = False

    for small_class in SMALL_CLASSES:
        # Look for columns that contain the small class name
        for col in row.index:
            if small_class in str(col) and pd.notna(row[col]):
                val = str(row[col]).strip().lower()
                # Convert various representations to 0/1
                if val in ("1", "true", "yes", "是", "有", "positive", "pos"):
                    labels[small_class] = 1
                    found_any_label = True
                elif val in ("0", "false", "no", "否", "无", "negative", "neg"):
                    labels[small_class] = 0
                    found_any_label = True
                break

    if found_any_label:
        # Fill missing labels with 0
        for small_class in SMALL_CLASSES:
            if small_class not in labels:
                labels[small_class] = 0
        return None, labels

    return None, None


def _parse_single_annotation_cell(
    cell_value: Any,
) -> Tuple[Optional[Dict[str, Any]], Optional[Dict[str, int]]]:
    """Parse a single annotation cell value."""
    if cell_value is None or (isinstance(cell_value, float) and pd.isna(cell_value)):
        return None, None

    s = str(cell_value).strip()
    if not s:
        return None, None

    # try JSON parsing
    if s.startswith("{") or s.startswith("["):
        try:
            data = json.loads(s)
            # review_res dict directly
            if isinstance(data, dict) and (
                "sensitive_inquiry" in data
                or "key_contact" in data
                or "government_inquiry" in data
            ):
                labels = review_res_to_smallclass_labels(data)
                return data, labels
            # if given directly as small-class labels mapping
            if isinstance(data, dict) and any(k in data for k in SMALL_CLASSES):
                labels = {k: int(bool(data.get(k))) for k in SMALL_CLASSES}
                return None, labels
        except Exception:
            pass

    # Try to parse as simple text indicators
    s_lower = s.lower()
    if s_lower in ("1", "true", "yes", "是", "有", "positive", "pos"):
        # This suggests a binary annotation, but we don't know which class
        # Return None for now, could be enhanced if we know the column context
        return None, None

    return None, None


def attach_matched_ids_and_fix_hit_rule(
    review_res: Dict[str, Any], messages: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Replicate attach_matched_ids logic locally, without importing pipeline.
    - Deduplicate values
    - Unescape HTML entities in values for matching
    - Keep only values that appear verbatim in cleaned messages
    - matched_ids is a 2D array aligned to values
    - If no validated values -> hit_rule=False for that item
    """

    def process_item(item: Dict[str, Any]):
        vals = item.get("values", []) or []
        # preserve order, dedup
        seen = set()
        uniq_vals = []
        for v in vals:
            v2 = str(v)
            if v2 not in seen:
                seen.add(v2)
                uniq_vals.append(v2)
        validated_values = []
        validated_ids = []
        for v in uniq_vals:
            vv = html_unescape(v)
            if not vv:
                continue
            ids = [m["id"] for m in messages if vv in (m.get("msg") or "")]
            if ids:
                validated_values.append(vv)
                # unique, order preserving
                seen_ids = []
                for mid in ids:
                    if mid not in seen_ids:
                        seen_ids.append(mid)
                validated_ids.append(seen_ids)
        item["values"] = validated_values
        item["matched_ids"] = validated_ids
        if not validated_values:
            item["hit_rule"] = False

    # iterate over review_res categories
    for _, items in list(review_res.items()):
        if isinstance(items, list):
            for item in items:
                if isinstance(item, dict) and "values" in item:
                    process_item(item)
        elif isinstance(items, dict) and "values" in items:
            process_item(items)
    return review_res


def main(
    input_dir: str = "data/gt",
    output_dir: str = "data/gt/processed",
    debug: bool = False,
):
    base = Path(input_dir)
    out = Path(output_dir)
    out.mkdir(parents=True, exist_ok=True)

    files = sorted(base.glob("gt_*.xlsx"))
    if not files:
        print(f"No Excel files found in {input_dir}")
        return

    # Accumulate by case
    cases_msgs: Dict[int, List[Message]] = defaultdict(list)
    cases_ann: Dict[int, Dict[str, Any]] = {}  # latest review_res per case if present
    cases_labels: Dict[int, Dict[str, int]] = {}

    total_rows = 0
    parsed_ann_rows = 0
    debug_info = {
        "files_analyzed": [],
        "columns_found": set(),
        "annotation_samples": [],
    }

    for f in files:
        print(f"Processing file: {f}")
        xls = pd.ExcelFile(f)
        file_info = {"filename": f.name, "sheets": xls.sheet_names}

        for sheet in xls.sheet_names:
            df = pd.read_excel(f, sheet_name=sheet)
            total_rows += len(df)

            if debug and len(debug_info["files_analyzed"]) == 0:
                # Debug info for first file only
                print(f"  Sheet: {sheet}, Rows: {len(df)}, Columns: {len(df.columns)}")
                print(f"  Columns: {list(df.columns)}")
                debug_info["columns_found"].update(df.columns)

                # Look for annotation columns
                annotation_keywords = [
                    "正确",
                    "审核",
                    "标注",
                    "结果",
                    "result",
                    "label",
                    "annotation",
                ]
                potential_cols = [
                    col
                    for col in df.columns
                    if any(
                        keyword in str(col).lower() for keyword in annotation_keywords
                    )
                ]
                if potential_cols:
                    print(f"  Potential annotation columns: {potential_cols}")
                    for col in potential_cols[:3]:  # Check first 3 potential columns
                        non_null_count = df[col].notna().sum()
                        if non_null_count > 0:
                            sample_val = df[col].dropna().iloc[0]
                            debug_info["annotation_samples"].append(
                                {
                                    "column": col,
                                    "sample_value": str(sample_val)[:200],
                                    "non_null_count": non_null_count,
                                }
                            )
                            print(f"    {col}: {non_null_count} non-null values")
                            print(f"    Sample: {str(sample_val)[:100]}...")

            for _, row in df.iterrows():
                case_id = to_int(row.get("f_case_id"), 0)
                msg_id = to_int(row.get("f_msg_id"), 0)
                role = normalize_role(row.get("f_user_type"))
                text = parse_f_original_msg(row.get("f_original_msg"))
                if text:
                    cases_msgs[case_id].append(
                        Message(
                            id=msg_id or len(cases_msgs[case_id]) + 1,
                            type=role,
                            msg=text,
                        )
                    )
                # parse human annotation for this row (case-level override)
                review_res_gt, labels = parse_annot_cols(row)
                if review_res_gt is not None:
                    cases_ann[case_id] = review_res_gt
                    cases_labels[case_id] = review_res_to_smallclass_labels(
                        review_res_gt
                    )
                    parsed_ann_rows += 1
                elif labels is not None:
                    # labels present without full review_res
                    cases_labels[case_id] = labels
                    parsed_ann_rows += 1

        file_info["annotation_rows_found"] = parsed_ann_rows
        debug_info["files_analyzed"].append(file_info)

    # Sort messages by id per case and deduplicate messages with identical content+role
    for cid, msgs in list(cases_msgs.items()):
        msgs_sorted = sorted(msgs, key=lambda m: m.id)
        seen = set()
        uniq: List[Message] = []
        for m in msgs_sorted:
            key = (m.type, m.msg)
            if key not in seen:
                seen.add(key)
                uniq.append(m)
        cases_msgs[cid] = uniq

    # After messages are cleaned, if we have review_res_gt for a case, attach matched_ids and fix hit_rule
    # Build dict messages for matching
    for cid in list(cases_ann.keys()):
        msgs = [asdict(m) for m in cases_msgs.get(cid, [])]
        cases_ann[cid] = attach_matched_ids_and_fix_hit_rule(cases_ann[cid], msgs)

    # Build outputs
    ts = datetime.utcnow().isoformat() + "Z"
    gt_path = out / "ground_truth.jsonl"
    labels_path = out / "labels.csv"
    stats_path = out / "stats.md"

    # Write ground_truth.jsonl
    rows_written = 0
    with open(gt_path, "w", encoding="utf-8") as fw:
        for cid in sorted(cases_msgs.keys()):
            rec: Dict[str, Any] = {
                "case_id": cid,
                "created_at": ts,
                "messages": [asdict(m) for m in cases_msgs[cid]],
            }
            if cid in cases_ann:
                rec["review_res_gt"] = cases_ann[cid]
            if cid in cases_labels:
                rec["labels"] = cases_labels[cid]
            fw.write(json.dumps(rec, ensure_ascii=False) + "\n")
            rows_written += 1

    # Write labels.csv (only cases having labels)
    label_records = []
    for cid, lbs in cases_labels.items():
        row = {"case_id": cid}
        row.update({k: lbs.get(k, 0) for k in SMALL_CLASSES})
        label_records.append(row)
    labels_df = pd.DataFrame(label_records)
    if not labels_df.empty:
        labels_df = labels_df.sort_values(by=["case_id"])  # type: ignore
        labels_df.to_csv(labels_path, index=False, encoding="utf-8")

    # Stats
    total_cases = len(cases_msgs)
    labeled_cases = len(cases_labels)
    with open(stats_path, "w", encoding="utf-8") as f:
        f.write(f"# Ground Truth Build Stats\n\n")
        f.write(f"- Total rows scanned: {total_rows}\n")
        f.write(f"- Total cases: {total_cases}\n")
        f.write(f"- Cases with human annotations parsed: {parsed_ann_rows}\n")
        f.write(f"- Cases with labels: {labeled_cases}\n")
        if labeled_cases:
            agg = defaultdict(int)
            for lbs in cases_labels.values():
                for k, v in lbs.items():
                    agg[k] += int(v)
            f.write("\n## Positive counts per small class\n")
            for k in SMALL_CLASSES:
                f.write(f"- {k}: {agg.get(k, 0)}\n")

    print(f"✅ Wrote {rows_written} cases to {gt_path}")
    if not labels_df.empty:
        print(f"✅ Wrote labels CSV to {labels_path} (cases: {len(labels_df)})")
    else:
        print(
            "ℹ️ No labels parsed from human annotation columns yet; labels.csv not created."
        )
    print(f"ℹ️ Stats written to {stats_path}")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument(
        "--input", default="data/gt", help="Input directory containing gt_*.xlsx"
    )
    ap.add_argument("--out", default="data/gt/processed", help="Output directory")
    ap.add_argument("--debug", action="store_true", help="Enable debug output")
    args = ap.parse_args()
    main(args.input, args.out, args.debug)
