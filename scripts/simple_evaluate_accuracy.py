#!/usr/bin/env python3
"""
简化版准确率评估工具 - 不依赖sklearn
"""

import argparse
import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import defaultdict

# 小类别列表
SMALL_CLASSES = [
    "政府机构",
    "政府邮箱", 
    "key_contact",
    "internal_system",
    "咨询公司信息",
    "兜售用户信息",
    "负面新闻",
    "重大客诉",
    "索要联系方式",
    "辱骂信息",
]

def load_ground_truth(gt_path: str) -> Dict[int, Dict[str, int]]:
    """加载ground truth数据"""
    gt_labels = {}
    
    if gt_path.endswith('.csv'):
        df = pd.read_csv(gt_path)
        for _, row in df.iterrows():
            case_id = int(row['case_id'])
            labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            gt_labels[case_id] = labels
    
    print(f"加载了 {len(gt_labels)} 个案例的ground truth标签")
    return gt_labels

def load_model_results(results_path: str) -> Dict[int, Dict[str, int]]:
    """加载模型输出结果"""
    model_labels = {}
    
    if results_path.endswith('.csv'):
        df = pd.read_csv(results_path)
        for _, row in df.iterrows():
            case_id = int(row['case_id'])
            labels = {cls: int(row.get(cls, 0)) for cls in SMALL_CLASSES}
            model_labels[case_id] = labels
    
    print(f"加载了 {len(model_labels)} 个案例的模型结果")
    return model_labels

def calculate_binary_metrics(tp: int, tn: int, fp: int, fn: int) -> Dict[str, float]:
    """计算二分类指标"""
    total = tp + tn + fp + fn
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    accuracy = (tp + tn) / total if total > 0 else 0.0
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'accuracy': accuracy,
        'tp': tp,
        'tn': tn,
        'fp': fp,
        'fn': fn,
        'support': tp + fn
    }

def calculate_metrics(gt_labels: Dict[int, Dict[str, int]], 
                     model_labels: Dict[int, Dict[str, int]]) -> Dict[str, Any]:
    """计算各种评估指标"""
    
    # 找到共同的案例ID
    common_case_ids = set(gt_labels.keys()) & set(model_labels.keys())
    print(f"共同案例数: {len(common_case_ids)}")
    
    if not common_case_ids:
        raise ValueError("没有找到共同的案例ID")
    
    metrics = {}
    class_metrics = {}
    
    # 计算每个类别的指标
    overall_tp = overall_tn = overall_fp = overall_fn = 0
    
    for cls in SMALL_CLASSES:
        tp = tn = fp = fn = 0
        
        for case_id in common_case_ids:
            gt_val = gt_labels[case_id][cls]
            pred_val = model_labels[case_id][cls]
            
            if gt_val == 1 and pred_val == 1:
                tp += 1
            elif gt_val == 0 and pred_val == 0:
                tn += 1
            elif gt_val == 0 and pred_val == 1:
                fp += 1
            elif gt_val == 1 and pred_val == 0:
                fn += 1
        
        # 累计到整体指标
        overall_tp += tp
        overall_tn += tn
        overall_fp += fp
        overall_fn += fn
        
        # 计算该类别的指标
        class_metrics[cls] = calculate_binary_metrics(tp, tn, fp, fn)
    
    # 计算整体微平均指标
    overall_metrics = calculate_binary_metrics(overall_tp, overall_tn, overall_fp, overall_fn)
    
    # 计算宏平均指标
    macro_precision = sum(m['precision'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    macro_recall = sum(m['recall'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    macro_f1 = sum(m['f1'] for m in class_metrics.values()) / len(SMALL_CLASSES)
    
    metrics['overall'] = {
        'micro_precision': overall_metrics['precision'],
        'micro_recall': overall_metrics['recall'],
        'micro_f1': overall_metrics['f1'],
        'micro_accuracy': overall_metrics['accuracy'],
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'total_cases': len(common_case_ids)
    }
    
    metrics['by_class'] = class_metrics
    
    return metrics

def print_metrics_report(metrics: Dict[str, Any]):
    """打印评估报告"""
    print("\n" + "="*80)
    print("准确率评估报告")
    print("="*80)
    
    # 整体指标
    overall = metrics['overall']
    print(f"\n整体指标 (基于 {overall['total_cases']} 个案例):")
    print(f"  微平均 - Precision: {overall['micro_precision']:.4f}, Recall: {overall['micro_recall']:.4f}, F1: {overall['micro_f1']:.4f}")
    print(f"  宏平均 - Precision: {overall['macro_precision']:.4f}, Recall: {overall['macro_recall']:.4f}, F1: {overall['macro_f1']:.4f}")
    print(f"  微平均准确率: {overall['micro_accuracy']:.4f}")
    
    # 按类别的详细指标
    print(f"\n按类别详细指标:")
    print(f"{'类别':<15} {'Precision':<10} {'Recall':<10} {'F1':<10} {'Accuracy':<10} {'Support':<8} {'TP':<4} {'FP':<4} {'FN':<4} {'TN':<4}")
    print("-" * 100)
    
    for cls in SMALL_CLASSES:
        m = metrics['by_class'][cls]
        print(f"{cls:<15} {m['precision']:<10.4f} {m['recall']:<10.4f} {m['f1']:<10.4f} {m['accuracy']:<10.4f} {m['support']:<8} {m['tp']:<4} {m['fp']:<4} {m['fn']:<4} {m['tn']:<4}")

def save_detailed_results(metrics: Dict[str, Any], output_path: str):
    """保存详细结果到文件"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=2)
    print(f"\n详细结果已保存到: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="评估模型输出结果的准确率")
    parser.add_argument("--gt", required=True, help="Ground truth文件路径 (.csv)")
    parser.add_argument("--pred", required=True, help="模型预测结果文件路径 (.csv)")
    parser.add_argument("--output", help="输出详细结果的文件路径 (可选)")
    
    args = parser.parse_args()
    
    try:
        # 加载数据
        print("加载ground truth数据...")
        gt_labels = load_ground_truth(args.gt)
        
        print("加载模型预测结果...")
        model_labels = load_model_results(args.pred)
        
        # 计算指标
        print("计算评估指标...")
        metrics = calculate_metrics(gt_labels, model_labels)
        
        # 打印报告
        print_metrics_report(metrics)
        
        # 保存详细结果
        if args.output:
            save_detailed_results(metrics, args.output)
        
    except Exception as e:
        print(f"评估过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
