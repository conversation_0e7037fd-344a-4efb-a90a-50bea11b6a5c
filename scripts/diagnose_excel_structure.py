#!/usr/bin/env python3
"""
诊断Excel文件结构，查看人工标注列的具体情况
"""

import pandas as pd
import json
from pathlib import Path
import sys

def diagnose_excel_file(filepath):
    """诊断单个Excel文件的结构"""
    print(f"\n{'='*60}")
    print(f"分析文件: {filepath}")
    print(f"{'='*60}")
    
    try:
        # 读取Excel文件
        xls = pd.ExcelFile(filepath)
        print(f"Sheet数量: {len(xls.sheet_names)}")
        print(f"Sheet名称: {xls.sheet_names}")
        
        for sheet_name in xls.sheet_names:
            print(f"\n--- Sheet: {sheet_name} ---")
            df = pd.read_excel(filepath, sheet_name=sheet_name)
            print(f"行数: {len(df)}, 列数: {len(df.columns)}")
            
            # 显示所有列名
            print(f"\n所有列名 ({len(df.columns)}个):")
            for i, col in enumerate(df.columns, 1):
                print(f"  {i:2d}. {col}")
            
            # 查找可能的标注列
            annotation_keywords = ['正确', '审核', '标注', '结果', 'result', 'label', 'annotation', '人工', '标记']
            potential_annot_cols = []
            for col in df.columns:
                col_str = str(col).lower()
                if any(keyword in col_str for keyword in annotation_keywords):
                    potential_annot_cols.append(col)
            
            if potential_annot_cols:
                print(f"\n可能的标注列:")
                for col in potential_annot_cols:
                    non_null_count = df[col].notna().sum()
                    print(f"  - {col}: {non_null_count}/{len(df)} 非空值")
                
                # 显示标注列的样例数据
                print(f"\n标注列样例数据:")
                for col in potential_annot_cols:
                    print(f"\n列: {col}")
                    non_null_values = df[col].dropna()
                    if len(non_null_values) > 0:
                        print(f"  非空值数量: {len(non_null_values)}")
                        print(f"  数据类型: {type(non_null_values.iloc[0])}")
                        
                        # 显示前3个非空值
                        for i, val in enumerate(non_null_values.head(3)):
                            val_str = str(val)
                            if len(val_str) > 200:
                                val_str = val_str[:200] + "..."
                            print(f"    样例{i+1}: {val_str}")
                            
                            # 尝试解析JSON
                            if val_str.strip().startswith('{') or val_str.strip().startswith('['):
                                try:
                                    parsed = json.loads(str(val))
                                    print(f"      -> JSON解析成功，类型: {type(parsed)}")
                                    if isinstance(parsed, dict):
                                        print(f"      -> 字典键: {list(parsed.keys())}")
                                except:
                                    print(f"      -> JSON解析失败")
                    else:
                        print(f"  该列无非空值")
            else:
                print(f"\n未找到明显的标注列")
                
            # 检查是否有包含特定小类别名称的列
            small_classes = ["政府机构", "政府邮箱", "key_contact", "internal_system", 
                           "咨询公司信息", "兜售用户信息", "负面新闻", "重大客诉", 
                           "索要联系方式", "辱骂信息"]
            
            class_cols = []
            for col in df.columns:
                if any(cls in str(col) for cls in small_classes):
                    class_cols.append(col)
            
            if class_cols:
                print(f"\n包含小类别名称的列:")
                for col in class_cols:
                    non_null_count = df[col].notna().sum()
                    unique_values = df[col].dropna().unique()
                    print(f"  - {col}: {non_null_count} 非空值, 唯一值: {list(unique_values)[:10]}")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    gt_dir = Path("data/gt")
    excel_files = sorted(gt_dir.glob("gt_*.xlsx"))
    
    if not excel_files:
        print("未找到gt_*.xlsx文件")
        return
    
    print(f"找到 {len(excel_files)} 个Excel文件:")
    for f in excel_files:
        print(f"  - {f}")
    
    # 分析每个文件
    for excel_file in excel_files:
        diagnose_excel_file(excel_file)
    
    print(f"\n{'='*60}")
    print("诊断完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
