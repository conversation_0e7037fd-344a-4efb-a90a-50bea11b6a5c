#!/usr/bin/env python3
"""
在所有ground truth数据上运行新模型并计算完整的准确率指标
"""

import json
import sys
import os
import pandas as pd
from pathlib import Path
from datetime import datetime
import traceback
from sklearn.metrics import (
    precision_score,
    recall_score,
    f1_score,
    classification_report,
    confusion_matrix,
)
import numpy as np

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))


def load_ground_truth_data():
    """加载ground truth数据"""
    print("📂 加载ground truth数据...")

    # 加载案例数据
    gt_file = "data/gt/processed/ground_truth.jsonl"
    if not Path(gt_file).exists():
        print(f"❌ Ground truth文件不存在: {gt_file}")
        return None, None

    cases = []
    with open(gt_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.strip():
                case_data = json.loads(line)
                cases.append(case_data)

    print(f"✅ 加载了 {len(cases)} 个案例")

    # 加载标签数据
    labels_file = "data/gt/processed/sample_labels.csv"
    labels_df = None
    if Path(labels_file).exists():
        labels_df = pd.read_csv(labels_file)
        print(f"✅ 加载了 {len(labels_df)} 个案例的标签")
    else:
        print("⚠️ 没有找到标签文件，将只运行检测不计算准确率")

    return cases, labels_df


def run_model_on_all_cases(cases):
    """在所有案例上运行模型"""
    print(f"\n🔍 开始在 {len(cases)} 个案例上运行新模型...")

    # 导入pipeline
    try:
        from dc_ai_red_line_review.main_pipe import BasicPipeline

        pipeline = BasicPipeline()
        print("✅ 成功初始化pipeline")
    except Exception as e:
        print(f"❌ 初始化pipeline失败: {e}")
        return None

    results = []
    success_count = 0
    error_count = 0

    categories = [
        "重大客诉",
        "索要联系方式",
        "兜售用户信息",
        "负面新闻",
        "辱骂信息",
        "咨询公司信息",
    ]
    category_stats = {cat: 0 for cat in categories}

    # 分批处理，每100个案例显示一次进度
    batch_size = 100
    total_batches = (len(cases) + batch_size - 1) // batch_size

    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(cases))
        batch_cases = cases[start_idx:end_idx]

        print(
            f"处理批次 {batch_idx + 1}/{total_batches} (案例 {start_idx + 1}-{end_idx})"
        )

        for i, case in enumerate(batch_cases):
            case_id = case.get("case_id", f"unknown_{start_idx + i}")
            messages = case.get("messages", [])

            try:
                result = pipeline.run(messages, str(case_id))

                # 提取标签
                labels = {cat: 0 for cat in categories}
                review_res = result.get("review_res", {})

                # 检查 sensitive_inquiry
                for item_data in review_res.get("sensitive_inquiry", []):
                    if item_data.get("hit_rule", False):
                        cat = item_data.get("type", "")
                        if cat in categories:
                            labels[cat] = 1
                            category_stats[cat] += 1

                # 检查 sensitive_reply
                for item_data in review_res.get("sensitive_reply", []):
                    if item_data.get("hit_rule", False):
                        cat = item_data.get("type", "")
                        if cat in categories:
                            labels[cat] = 1
                            category_stats[cat] += 1

                results.append(
                    {
                        "case_id": case_id,
                        "status": "success",
                        "labels": labels,
                        "result": result,
                        "message_count": len(messages),
                    }
                )

                success_count += 1

            except Exception as e:
                print(f"  案例 {case_id} 处理失败: {e}")
                results.append(
                    {
                        "case_id": case_id,
                        "status": "error",
                        "error": str(e),
                        "message_count": len(messages),
                    }
                )
                error_count += 1

        # 显示批次进度
        batch_success = len(
            [r for r in results[start_idx:end_idx] if r.get("status") == "success"]
        )
        print(f"  批次完成: {batch_success}/{len(batch_cases)} 成功")

    print(f"\n📊 模型运行完成:")
    print(f"  成功: {success_count}")
    print(f"  失败: {error_count}")
    print(f"  成功率: {success_count / (success_count + error_count) * 100:.1f}%")

    return results, category_stats


def calculate_accuracy_metrics(results, labels_df):
    """计算准确率指标"""
    if labels_df is None:
        print("⚠️ 没有标签数据，跳过准确率计算")
        return None

    print(f"\n📈 计算准确率指标...")

    # 准备数据
    categories = [
        "重大客诉",
        "索要联系方式",
        "兜售用户信息",
        "负面新闻",
        "辱骂信息",
        "咨询公司信息",
    ]

    # 创建结果映射
    results_map = {}
    for result in results:
        if result.get("status") == "success":
            case_id = int(result.get("case_id"))
            labels = result.get("labels", {})
            results_map[case_id] = labels

    # 创建标签映射
    labels_map = {}
    for _, row in labels_df.iterrows():
        case_id = int(row["case_id"])
        labels = {cat: int(row[cat]) for cat in categories}
        labels_map[case_id] = labels

    # 找到共同案例
    common_cases = set(results_map.keys()) & set(labels_map.keys())

    if not common_cases:
        print("❌ 没有找到共同案例进行准确率计算")
        return None

    print(f"✅ 找到 {len(common_cases)} 个有标签的案例")

    # 准备预测和真实标签
    y_true = []
    y_pred = []

    for case_id in common_cases:
        true_labels = labels_map[case_id]
        pred_labels = results_map[case_id]

        for cat in categories:
            y_true.append(true_labels[cat])
            y_pred.append(pred_labels[cat])

    # 计算指标
    precision = precision_score(y_true, y_pred, average="macro", zero_division=0)
    recall = recall_score(y_true, y_pred, average="macro", zero_division=0)
    f1 = f1_score(y_true, y_pred, average="macro", zero_division=0)

    # 按类别计算指标
    category_metrics = {}
    for i, cat in enumerate(categories):
        cat_true = [y_true[j] for j in range(i, len(y_true), len(categories))]
        cat_pred = [y_pred[j] for j in range(i, len(y_pred), len(categories))]

        if len(set(cat_true)) > 1:  # 只有当类别有正负样本时才计算
            cat_precision = precision_score(cat_true, cat_pred, zero_division=0)
            cat_recall = recall_score(cat_true, cat_pred, zero_division=0)
            cat_f1 = f1_score(cat_true, cat_pred, zero_division=0)
        else:
            cat_precision = cat_recall = cat_f1 = 0.0

        category_metrics[cat] = {
            "precision": cat_precision,
            "recall": cat_recall,
            "f1": cat_f1,
            "true_positives": sum(
                1 for j in range(len(cat_true)) if cat_true[j] == 1 and cat_pred[j] == 1
            ),
            "false_positives": sum(
                1 for j in range(len(cat_true)) if cat_true[j] == 0 and cat_pred[j] == 1
            ),
            "false_negatives": sum(
                1 for j in range(len(cat_true)) if cat_true[j] == 1 and cat_pred[j] == 0
            ),
            "true_negatives": sum(
                1 for j in range(len(cat_true)) if cat_true[j] == 0 and cat_pred[j] == 0
            ),
        }

    metrics = {
        "overall": {
            "precision": precision,
            "recall": recall,
            "f1": f1,
            "common_cases": len(common_cases),
        },
        "by_category": category_metrics,
    }

    return metrics


def print_accuracy_report(metrics, category_stats, total_successful_cases):
    """打印准确率报告"""
    print(f"\n📊 新模型完整准确率报告")
    print("=" * 80)

    if metrics is None:
        print("⚠️ 无法计算准确率指标（缺少标签数据）")
        return

    overall = metrics["overall"]
    print(f"整体指标 (基于 {overall['common_cases']} 个有标签案例):")
    print(f"  精确率 (Precision): {overall['precision']:.3f}")
    print(f"  召回率 (Recall): {overall['recall']:.3f}")
    print(f"  F1分数: {overall['f1']:.3f}")

    print(f"\n各类别详细指标:")
    print("-" * 80)
    print(
        f"{'类别':<15} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'TP':<4} {'FP':<4} {'FN':<4} {'TN':<4}"
    )
    print("-" * 80)

    for cat, cat_metrics in metrics["by_category"].items():
        print(
            f"{cat:<15} {cat_metrics['precision']:<8.3f} {cat_metrics['recall']:<8.3f} "
            f"{cat_metrics['f1']:<8.3f} {cat_metrics['true_positives']:<4} "
            f"{cat_metrics['false_positives']:<4} {cat_metrics['false_negatives']:<4} "
            f"{cat_metrics['true_negatives']:<4}"
        )

    print(f"\n检测频率统计 (全部 {total_successful_cases} 个案例):")
    print("-" * 50)
    for cat, count in category_stats.items():
        percentage = (
            (count / total_successful_cases) * 100 if total_successful_cases > 0 else 0
        )
        print(f"{cat:<15}: {count:>4} 次 ({percentage:>5.1f}%)")


def save_results(results, metrics, category_stats):
    """保存结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存详细结果
    output_data = {
        "timestamp": timestamp,
        "model_name": os.environ.get("MODEL_NAME", "unknown"),
        "total_cases": len(results),
        "successful_cases": len([r for r in results if r.get("status") == "success"]),
        "accuracy_metrics": metrics,
        "category_statistics": category_stats,
        "detailed_results": results,
    }

    output_file = f"results/full_accuracy_test_{timestamp}.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    # 保存简化的CSV报告
    if metrics:
        csv_data = []
        for cat, cat_metrics in metrics["by_category"].items():
            csv_data.append(
                {
                    "类别": cat,
                    "精确率": f"{cat_metrics['precision']:.3f}",
                    "召回率": f"{cat_metrics['recall']:.3f}",
                    "F1分数": f"{cat_metrics['f1']:.3f}",
                    "检测次数": category_stats.get(cat, 0),
                    "TP": cat_metrics["true_positives"],
                    "FP": cat_metrics["false_positives"],
                    "FN": cat_metrics["false_negatives"],
                    "TN": cat_metrics["true_negatives"],
                }
            )

        csv_file = f"results/accuracy_metrics_{timestamp}.csv"
        pd.DataFrame(csv_data).to_csv(csv_file, index=False, encoding="utf-8")
        print(f"📊 CSV报告已保存到: {csv_file}")

    print(f"📄 详细结果已保存到: {output_file}")
    return output_file


def main():
    """主函数"""
    print("🚀 新模型完整准确率测试")
    print("=" * 60)

    # 1. 加载数据
    cases, labels_df = load_ground_truth_data()
    if cases is None:
        return

    # 2. 运行模型
    results, category_stats = run_model_on_all_cases(cases)
    if results is None:
        return

    # 3. 计算准确率
    metrics = calculate_accuracy_metrics(results, labels_df)

    # 4. 打印报告
    successful_cases = len([r for r in results if r.get("status") == "success"])
    print_accuracy_report(metrics, category_stats, successful_cases)

    # 5. 保存结果
    output_file = save_results(results, metrics, category_stats)

    print(f"\n✅ 完整准确率测试完成！")
    print(f"📄 详细结果: {output_file}")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 运行过程中出错: {e}")
        traceback.print_exc()
