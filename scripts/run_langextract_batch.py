#!/usr/bin/env python3
"""
批量运行LangExtract版本的红线检测并生成结果
"""

import json
import sys
import os
from pathlib import Path
from datetime import datetime
import importlib.util

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_langextract_module():
    """动态加载LangExtract模块"""
    module_path = Path("long_text/red_line_lx.py")
    if not module_path.exists():
        raise FileNotFoundError(f"LangExtract模块不存在: {module_path}")
    
    spec = importlib.util.spec_from_file_location("red_line_lx", module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def load_individual_cases(data_dir="data/individual_cases"):
    """加载individual_cases数据"""
    data_path = Path(data_dir)
    if not data_path.exists():
        raise FileNotFoundError(f"数据目录不存在: {data_path}")
    
    cases = []
    json_files = list(data_path.glob("case_*.json"))
    
    print(f"找到 {len(json_files)} 个案例文件")
    
    for json_file in sorted(json_files)[:50]:  # 先处理50个案例进行测试
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                case_data = json.load(f)
                cases.append(case_data)
        except Exception as e:
            print(f"读取文件 {json_file} 时出错: {e}")
            continue
    
    return cases

def run_langextract_batch():
    """批量运行LangExtract检测"""
    
    # 检查环境变量
    required_env_vars = ["MODEL_NAME", "QWQ_API_KEY", "QWQ_BASE_URL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {missing_vars}")
        print("请确保设置了以下环境变量:")
        for var in required_env_vars:
            print(f"  - {var}")
        return
    
    # 加载模块和数据
    print("🔍 加载LangExtract模块...")
    try:
        lx_module = load_langextract_module()
    except Exception as e:
        print(f"❌ 加载LangExtract模块失败: {e}")
        return
    
    print("📂 加载案例数据...")
    try:
        cases = load_individual_cases()
    except Exception as e:
        print(f"❌ 加载案例数据失败: {e}")
        return
    
    print(f"📊 开始处理 {len(cases)} 个案例...")
    
    # 处理结果
    results = []
    success_count = 0
    error_count = 0
    
    for i, case in enumerate(cases):
        case_id = case.get('case_id', f'unknown_{i}')
        messages = case.get('messages', [])
        
        print(f"处理案例 {i+1}/{len(cases)}: {case_id}")
        
        try:
            # 运行LangExtract检测
            review_result = lx_module.run_review(messages)
            
            result = {
                "case_id": case_id,
                "status": "success",
                "result": {
                    "id": case_id,
                    "review_res": review_result
                },
                "message_count": len(messages),
                "processed_at": datetime.now().isoformat()
            }
            
            results.append(result)
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            result = {
                "case_id": case_id,
                "status": "error",
                "error": str(e),
                "message_count": len(messages),
                "processed_at": datetime.now().isoformat()
            }
            results.append(result)
            error_count += 1
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results/langextract_results_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ 处理完成!")
    print(f"📊 统计:")
    print(f"  - 成功: {success_count}")
    print(f"  - 失败: {error_count}")
    print(f"  - 总计: {len(results)}")
    print(f"📁 结果已保存到: {output_file}")
    
    return output_file

if __name__ == "__main__":
    run_langextract_batch()
