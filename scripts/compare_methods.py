#!/usr/bin/env python3
"""
对比main_pipe.py和LangExtract两种方法的准确率
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Any, Tuple

# 六个关键类别
TARGET_CATEGORIES = [
    "重大客诉",
    "索要联系方式", 
    "兜售用户信息",
    "负面新闻",
    "辱骂信息",
    "咨询公司信息"
]

def extract_categories_from_result(review_res: Dict[str, Any]) -> Dict[str, int]:
    """从review_res中提取六个类别的标签"""
    labels = {category: 0 for category in TARGET_CATEGORIES}
    
    # 检查 sensitive_inquiry
    for item_data in review_res.get('sensitive_inquiry', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in TARGET_CATEGORIES:
                labels[category] = 1
    
    # 检查 sensitive_reply
    for item_data in review_res.get('sensitive_reply', []):
        if item_data.get('hit_rule', False):
            category = item_data.get('type', '')
            if category in TARGET_CATEGORIES:
                labels[category] = 1
    
    return labels

def load_main_pipe_results(file_path: str) -> Dict[str, Dict[str, int]]:
    """加载main_pipe.py的结果"""
    results = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for item in data:
        case_id = str(item.get('case_id', ''))
        if item.get('status') == 'success':
            result = item.get('result', {})
            review_res = result.get('review_res', {})
            labels = extract_categories_from_result(review_res)
            results[case_id] = labels
    
    return results

def load_langextract_results(file_path: str) -> Dict[str, Dict[str, int]]:
    """加载LangExtract的结果"""
    results = {}
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    for item in data:
        case_id = str(item.get('case_id', ''))
        if item.get('status') == 'success':
            result = item.get('result', {})
            review_res = result.get('review_res', {})
            labels = extract_categories_from_result(review_res)
            results[case_id] = labels
    
    return results

def calculate_comparison_metrics(method1_results: Dict[str, Dict[str, int]], 
                               method2_results: Dict[str, Dict[str, int]],
                               method1_name: str = "Method1",
                               method2_name: str = "Method2") -> Dict[str, Any]:
    """计算两种方法的对比指标"""
    
    # 找到共同的案例
    common_cases = set(method1_results.keys()) & set(method2_results.keys())
    
    if not common_cases:
        return {"error": "没有找到共同的案例"}
    
    metrics = {
        "common_cases": len(common_cases),
        "method1_name": method1_name,
        "method2_name": method2_name,
        "category_comparison": {},
        "overall_comparison": {}
    }
    
    # 按类别对比
    for category in TARGET_CATEGORIES:
        method1_hits = sum(1 for case_id in common_cases if method1_results[case_id][category] == 1)
        method2_hits = sum(1 for case_id in common_cases if method2_results[case_id][category] == 1)
        
        # 一致性分析
        agreement = sum(1 for case_id in common_cases 
                       if method1_results[case_id][category] == method2_results[case_id][category])
        agreement_rate = agreement / len(common_cases)
        
        # 差异分析
        method1_only = sum(1 for case_id in common_cases 
                          if method1_results[case_id][category] == 1 and method2_results[case_id][category] == 0)
        method2_only = sum(1 for case_id in common_cases 
                          if method1_results[case_id][category] == 0 and method2_results[case_id][category] == 1)
        
        metrics["category_comparison"][category] = {
            f"{method1_name}_hits": method1_hits,
            f"{method2_name}_hits": method2_hits,
            "agreement_rate": agreement_rate,
            f"{method1_name}_only": method1_only,
            f"{method2_name}_only": method2_only,
            "hit_rate_diff": (method2_hits - method1_hits) / len(common_cases)
        }
    
    # 整体对比
    method1_total_hits = sum(sum(method1_results[case_id].values()) for case_id in common_cases)
    method2_total_hits = sum(sum(method2_results[case_id].values()) for case_id in common_cases)
    
    method1_cases_with_hits = len([case_id for case_id in common_cases 
                                  if any(method1_results[case_id].values())])
    method2_cases_with_hits = len([case_id for case_id in common_cases 
                                  if any(method2_results[case_id].values())])
    
    metrics["overall_comparison"] = {
        f"{method1_name}_total_hits": method1_total_hits,
        f"{method2_name}_total_hits": method2_total_hits,
        f"{method1_name}_cases_with_hits": method1_cases_with_hits,
        f"{method2_name}_cases_with_hits": method2_cases_with_hits,
        f"{method1_name}_avg_hits_per_case": method1_total_hits / len(common_cases),
        f"{method2_name}_avg_hits_per_case": method2_total_hits / len(common_cases),
        "hit_detection_agreement": len([case_id for case_id in common_cases 
                                       if (any(method1_results[case_id].values()) == 
                                           any(method2_results[case_id].values()))]) / len(common_cases)
    }
    
    return metrics

def print_comparison_report(metrics: Dict[str, Any]):
    """打印对比报告"""
    if "error" in metrics:
        print(f"❌ {metrics['error']}")
        return
    
    method1_name = metrics["method1_name"]
    method2_name = metrics["method2_name"]
    
    print("=" * 80)
    print(f"📊 {method1_name} vs {method2_name} 对比分析报告")
    print("=" * 80)
    print(f"共同案例数: {metrics['common_cases']}")
    
    # 整体对比
    overall = metrics["overall_comparison"]
    print(f"\n🎯 整体对比:")
    print("-" * 50)
    print(f"{method1_name} 总命中次数: {overall[f'{method1_name}_total_hits']}")
    print(f"{method2_name} 总命中次数: {overall[f'{method2_name}_total_hits']}")
    print(f"{method1_name} 有命中的案例: {overall[f'{method1_name}_cases_with_hits']} ({overall[f'{method1_name}_cases_with_hits']/metrics['common_cases']*100:.1f}%)")
    print(f"{method2_name} 有命中的案例: {overall[f'{method2_name}_cases_with_hits']} ({overall[f'{method2_name}_cases_with_hits']/metrics['common_cases']*100:.1f}%)")
    print(f"检测一致性: {overall['hit_detection_agreement']*100:.1f}%")
    
    # 按类别对比
    print(f"\n📈 按类别详细对比:")
    print("-" * 80)
    print(f"{'类别':<15} {method1_name+'命中':<10} {method2_name+'命中':<10} {'一致性':<8} {'差异':<15}")
    print("-" * 80)
    
    for category in TARGET_CATEGORIES:
        comp = metrics["category_comparison"][category]
        method1_hits = comp[f"{method1_name}_hits"]
        method2_hits = comp[f"{method2_name}_hits"]
        agreement = comp["agreement_rate"]
        diff = comp["hit_rate_diff"]
        
        diff_str = f"{diff:+.1%}"
        print(f"{category:<15} {method1_hits:<10} {method2_hits:<10} {agreement:<8.1%} {diff_str:<15}")
    
    # 差异分析
    print(f"\n🔍 差异分析:")
    print("-" * 50)
    for category in TARGET_CATEGORIES:
        comp = metrics["category_comparison"][category]
        method1_only = comp[f"{method1_name}_only"]
        method2_only = comp[f"{method2_name}_only"]
        
        if method1_only > 0 or method2_only > 0:
            print(f"{category}:")
            if method1_only > 0:
                print(f"  仅{method1_name}检测到: {method1_only} 个案例")
            if method2_only > 0:
                print(f"  仅{method2_name}检测到: {method2_only} 个案例")

def save_comparison_results(metrics: Dict[str, Any], output_file: str):
    """保存对比结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=2)

def main():
    """主函数"""
    
    # 文件路径
    main_pipe_file = "results/individual_cases_results_20250811_104748.json"
    
    # 查找最新的LangExtract结果文件
    results_dir = Path("results")
    langextract_files = list(results_dir.glob("langextract_results_*.json"))
    
    if not Path(main_pipe_file).exists():
        print(f"❌ main_pipe结果文件不存在: {main_pipe_file}")
        return
    
    if not langextract_files:
        print("❌ 未找到LangExtract结果文件")
        print("请先运行: uv run python scripts/run_langextract_batch.py")
        return
    
    # 使用最新的LangExtract结果文件
    langextract_file = max(langextract_files, key=lambda x: x.stat().st_mtime)
    
    print(f"📂 加载数据...")
    print(f"  Main Pipe: {main_pipe_file}")
    print(f"  LangExtract: {langextract_file}")
    
    # 加载结果
    try:
        main_pipe_results = load_main_pipe_results(main_pipe_file)
        langextract_results = load_langextract_results(langextract_file)
    except Exception as e:
        print(f"❌ 加载结果文件失败: {e}")
        return
    
    print(f"Main Pipe 结果数: {len(main_pipe_results)}")
    print(f"LangExtract 结果数: {len(langextract_results)}")
    
    # 计算对比指标
    metrics = calculate_comparison_metrics(
        main_pipe_results, 
        langextract_results,
        "MainPipe",
        "LangExtract"
    )
    
    # 打印报告
    print_comparison_report(metrics)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results/method_comparison_{timestamp}.json"
    save_comparison_results(metrics, output_file)
    
    print(f"\n✅ 对比结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
