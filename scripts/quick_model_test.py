#!/usr/bin/env python3
"""
快速测试新模型
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_environment():
    """测试环境配置"""
    print("🔧 检查环境配置...")
    
    required_vars = ["MODEL_NAME", "QWQ_API_KEY", "QWQ_BASE_URL"]
    for var in required_vars:
        value = os.environ.get(var, "未设置")
        if var == "QWQ_API_KEY" and value != "未设置":
            value = value[:10] + "..." if len(value) > 10 else value
        print(f"  {var}: {value}")
    
    missing = [var for var in required_vars if not os.environ.get(var)]
    if missing:
        print(f"❌ 缺少环境变量: {missing}")
        return False
    
    print("✅ 环境配置正常")
    return True

def test_langextract_import():
    """测试LangExtract模块导入"""
    print("\n📦 测试LangExtract模块...")
    
    try:
        # 检查文件是否存在
        lx_file = Path("long_text/red_line_lx.py")
        if not lx_file.exists():
            print(f"❌ 文件不存在: {lx_file}")
            return False
        
        print(f"✅ 找到文件: {lx_file}")
        
        # 尝试导入
        import importlib.util
        spec = importlib.util.spec_from_file_location("red_line_lx", lx_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✅ 模块导入成功")
        
        # 测试函数是否存在
        if hasattr(module, 'run_review'):
            print("✅ run_review函数存在")
        else:
            print("❌ run_review函数不存在")
            return False
        
        return module
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_simple_case(lx_module):
    """测试简单案例"""
    print("\n🧪 测试简单案例...")
    
    # 创建测试消息
    test_messages = [
        {"id": 1, "type": "USER", "msg": "你们公司的注册地址在哪里？"},
        {"id": 2, "type": "AGENT", "msg": "我帮您查询"},
        {"id": 3, "type": "USER", "msg": "听说KuCoin跑路了是真的吗？"},
        {"id": 4, "type": "AGENT", "msg": "请加我的微信 wechat_123"},
        {"id": 5, "type": "USER", "msg": "你们这个垃圾平台！"}
    ]
    
    try:
        result = lx_module.run_review(test_messages)
        print("✅ 检测成功")
        
        # 分析结果
        print("\n📊 检测结果:")
        
        # 检查 sensitive_inquiry
        si_results = result.get('sensitive_inquiry', [])
        for item in si_results:
            if item.get('hit_rule', False):
                print(f"  ✅ {item.get('type', '')}: {len(item.get('values', []))} 个匹配")
            else:
                print(f"  ⚪ {item.get('type', '')}: 无匹配")
        
        # 检查 sensitive_reply
        sr_results = result.get('sensitive_reply', [])
        for item in sr_results:
            if item.get('hit_rule', False):
                print(f"  ✅ {item.get('type', '')}: {len(item.get('values', []))} 个匹配")
            else:
                print(f"  ⚪ {item.get('type', '')}: 无匹配")
        
        return result
        
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def load_real_case():
    """加载一个真实案例进行测试"""
    print("\n📂 加载真实案例...")
    
    data_path = Path("data/individual_cases")
    if not data_path.exists():
        print(f"❌ 数据目录不存在: {data_path}")
        return None
    
    json_files = list(data_path.glob("case_*.json"))
    if not json_files:
        print("❌ 没有找到案例文件")
        return None
    
    # 取第一个文件
    first_file = json_files[0]
    print(f"📄 读取文件: {first_file.name}")
    
    try:
        with open(first_file, 'r', encoding='utf-8') as f:
            case_data = json.load(f)
        
        case_id = case_data.get('case_id', 'unknown')
        messages = case_data.get('messages', [])
        
        print(f"  案例ID: {case_id}")
        print(f"  消息数量: {len(messages)}")
        
        return case_data
        
    except Exception as e:
        print(f"❌ 读取案例失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 新模型快速测试")
    print("=" * 50)
    
    # 1. 测试环境
    if not test_environment():
        return
    
    # 2. 测试模块导入
    lx_module = test_langextract_import()
    if not lx_module:
        return
    
    # 3. 测试简单案例
    simple_result = test_simple_case(lx_module)
    if not simple_result:
        return
    
    # 4. 测试真实案例
    real_case = load_real_case()
    if real_case:
        print(f"\n🧪 测试真实案例: {real_case.get('case_id', 'unknown')}")
        try:
            real_result = lx_module.run_review(real_case.get('messages', []))
            print("✅ 真实案例检测成功")
            
            # 统计命中
            total_hits = 0
            for category_list in [real_result.get('sensitive_inquiry', []), real_result.get('sensitive_reply', [])]:
                for item in category_list:
                    if item.get('hit_rule', False):
                        total_hits += 1
                        print(f"  ✅ {item.get('type', '')}: {item.get('values', [])[:1]}")  # 只显示第一个值
            
            if total_hits == 0:
                print("  ⚪ 该案例无风险检测")
            
        except Exception as e:
            print(f"❌ 真实案例检测失败: {e}")
    
    print(f"\n✅ 测试完成！新模型工作正常。")
    print(f"💡 现在可以运行完整的对比测试:")
    print(f"   uv run python scripts/run_new_model_comparison.py")

if __name__ == "__main__":
    main()
