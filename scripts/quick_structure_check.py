#!/usr/bin/env python3
"""
快速检查Excel文件结构，只读取少量数据
"""

import pandas as pd
import json
from pathlib import Path

def main():
    """快速分析Excel文件结构"""
    gt_dir = Path("data/gt")
    excel_files = sorted(gt_dir.glob("gt_*.xlsx"))
    
    if not excel_files:
        print("未找到gt_*.xlsx文件")
        return
    
    # 只分析第一个文件
    first_file = excel_files[0]
    print(f"分析文件: {first_file}")
    
    try:
        # 只读取列名
        df_header = pd.read_excel(first_file, nrows=0)
        columns = list(df_header.columns)
        print(f"总列数: {len(columns)}")
        
        print("\n所有列名:")
        for i, col in enumerate(columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 查找可能的标注列
        annotation_keywords = ['正确', '审核', '标注', '结果', 'result', 'label', 'annotation', '人工', '标记']
        potential_annot_cols = []
        
        for col in columns:
            col_str = str(col).lower()
            if any(keyword in col_str for keyword in annotation_keywords):
                potential_annot_cols.append(col)
        
        print(f"\n可能的标注列: {potential_annot_cols}")
        
        # 检查小类别列
        small_classes = ["政府机构", "政府邮箱", "key_contact", "internal_system", 
                        "咨询公司信息", "兜售用户信息", "负面新闻", "重大客诉", 
                        "索要联系方式", "辱骂信息"]
        
        class_cols = []
        for col in columns:
            if any(cls in str(col) for cls in small_classes):
                class_cols.append(col)
        
        print(f"包含小类别名称的列: {class_cols}")
        
        # 如果有潜在的标注列，读取少量数据查看内容
        if potential_annot_cols or class_cols:
            print(f"\n读取前10行数据进行内容分析...")
            
            # 选择要读取的列
            cols_to_read = ['f_case_id', 'f_msg_id']
            if potential_annot_cols:
                cols_to_read.extend(potential_annot_cols[:3])  # 最多3个标注列
            if class_cols:
                cols_to_read.extend(class_cols[:5])  # 最多5个小类别列
            
            # 过滤掉不存在的列
            cols_to_read = [col for col in cols_to_read if col in columns]
            
            df_sample = pd.read_excel(first_file, usecols=cols_to_read, nrows=10)
            
            print(f"\n样本数据分析 (前10行):")
            for col in cols_to_read:
                if col in ['f_case_id', 'f_msg_id']:
                    continue
                    
                non_null_count = df_sample[col].notna().sum()
                print(f"\n列: {col}")
                print(f"  非空值: {non_null_count}/10")
                
                if non_null_count > 0:
                    unique_vals = df_sample[col].dropna().unique()
                    print(f"  唯一值数量: {len(unique_vals)}")
                    
                    # 显示前几个值
                    for i, val in enumerate(unique_vals[:3]):
                        val_str = str(val)
                        if len(val_str) > 100:
                            val_str = val_str[:100] + "..."
                        print(f"    值{i+1}: {val_str}")
                        
                        # 检查是否是JSON
                        if val_str.strip().startswith('{'):
                            try:
                                parsed = json.loads(str(val))
                                print(f"      -> JSON解析成功，键: {list(parsed.keys())[:5]}")
                            except:
                                print(f"      -> JSON解析失败")
        
        print(f"\n建议:")
        if potential_annot_cols:
            print(f"- 发现潜在标注列，建议检查这些列的内容格式")
        elif class_cols:
            print(f"- 发现小类别列，可能使用分列标注格式")
        else:
            print(f"- 未发现明显的标注列，可能需要人工确认列名")
            print(f"- 或者标注数据可能在其他格式的文件中")
        
    except Exception as e:
        print(f"分析时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
