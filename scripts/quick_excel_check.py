#!/usr/bin/env python3
"""
快速检查Excel文件结构
"""

import pandas as pd
import json

def main():
    try:
        # 只读取第一个文件的前100行
        print("读取 gt_1.xlsx 的前100行...")
        df = pd.read_excel("data/gt/gt_1.xlsx", nrows=100)
        
        print(f"形状: {df.shape}")
        print(f"列数: {len(df.columns)}")
        
        print("\n所有列名:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 查找可能的标注列
        annotation_keywords = ['正确', '审核', '标注', '结果', 'result', 'label']
        potential_cols = [col for col in df.columns 
                         if any(keyword in str(col).lower() for keyword in annotation_keywords)]
        
        print(f"\n可能的标注列: {potential_cols}")
        
        if potential_cols:
            for col in potential_cols:
                non_null = df[col].notna().sum()
                print(f"\n{col}: {non_null} 非空值")
                if non_null > 0:
                    sample = df[col].dropna().iloc[0]
                    print(f"  样例: {str(sample)[:200]}")
        
        # 检查是否有小类别相关的列
        small_classes = ["政府机构", "政府邮箱", "key_contact", "咨询公司", "负面新闻", "辱骂"]
        class_cols = [col for col in df.columns 
                     if any(cls in str(col) for cls in small_classes)]
        
        if class_cols:
            print(f"\n包含小类别的列: {class_cols}")
            for col in class_cols:
                unique_vals = df[col].dropna().unique()
                print(f"  {col}: {list(unique_vals)[:5]}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
