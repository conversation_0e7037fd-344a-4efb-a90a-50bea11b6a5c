#!/usr/bin/env python3
"""
提取六个关键类别的检测结果并保存到文件
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# 目标六个类别
TARGET_CATEGORIES = [
    "重大客诉",
    "索要联系方式", 
    "兜售用户信息",
    "负面新闻",
    "辱骂信息",
    "咨询公司信息"
]

def extract_six_categories_results(json_file: str):
    """提取六个类别的检测结果"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 提取六个关键类别的检测结果")
    print("=" * 60)
    print(f"总案例数: {len(data)}")
    
    # 存储结果
    results = []
    category_stats = defaultdict(int)
    
    for item in data:
        case_id = item.get('case_id', '')
        result = item.get('result', {})
        review_res = result.get('review_res', {})
        
        # 初始化该案例的标签
        case_labels = {category: 0 for category in TARGET_CATEGORIES}
        case_details = {category: [] for category in TARGET_CATEGORIES}
        
        # 检查 sensitive_inquiry (包含: 咨询公司信息, 兜售用户信息, 负面新闻, 重大客诉)
        for item_data in review_res.get('sensitive_inquiry', []):
            if item_data.get('hit_rule', False):
                category = item_data.get('type', '')
                if category in TARGET_CATEGORIES:
                    case_labels[category] = 1
                    case_details[category].extend(item_data.get('values', []))
                    category_stats[category] += 1
        
        # 检查 sensitive_reply (包含: 索要联系方式, 辱骂信息)
        for item_data in review_res.get('sensitive_reply', []):
            if item_data.get('hit_rule', False):
                category = item_data.get('type', '')
                if category in TARGET_CATEGORIES:
                    case_labels[category] = 1
                    case_details[category].extend(item_data.get('values', []))
                    category_stats[category] += 1
        
        # 构建结果记录
        result_record = {
            'case_id': case_id,
            'processed_at': item.get('processed_at', ''),
            'message_count': item.get('message_count', 0)
        }
        
        # 添加六个类别的标签
        for category in TARGET_CATEGORIES:
            result_record[category] = case_labels[category]
        
        # 添加详细信息（可选）
        for category in TARGET_CATEGORIES:
            if case_details[category]:
                result_record[f'{category}_details'] = '; '.join(case_details[category][:3])  # 最多3个样例
            else:
                result_record[f'{category}_details'] = ''
        
        results.append(result_record)
    
    return results, category_stats

def save_results(results, category_stats, output_dir="results"):
    """保存结果到多种格式的文件"""
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 1. 保存为CSV格式（标签矩阵）
    df = pd.DataFrame(results)
    csv_file = output_path / f"six_categories_results_{timestamp}.csv"
    df.to_csv(csv_file, index=False, encoding='utf-8')
    print(f"✅ CSV文件已保存: {csv_file}")
    
    # 2. 保存为JSON格式（完整数据）
    json_file = output_path / f"six_categories_results_{timestamp}.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"✅ JSON文件已保存: {json_file}")
    
    # 3. 保存统计报告
    report_file = output_path / f"six_categories_report_{timestamp}.md"
    
    total_cases = len(results)
    cases_with_any_hit = len([r for r in results if any(r[cat] == 1 for cat in TARGET_CATEGORIES)])
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("# 六个关键类别检测结果报告\n\n")
        f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"**总案例数**: {total_cases}\n\n")
        f.write(f"**有风险检测的案例数**: {cases_with_any_hit} ({cases_with_any_hit/total_cases*100:.1f}%)\n\n")
        
        f.write("## 各类别检测统计\n\n")
        f.write("| 类别 | 命中案例数 | 占总案例比例 | 占风险案例比例 |\n")
        f.write("|------|------------|--------------|----------------|\n")
        
        for category in TARGET_CATEGORIES:
            count = category_stats[category]
            total_pct = (count / total_cases) * 100
            risk_pct = (count / cases_with_any_hit) * 100 if cases_with_any_hit > 0 else 0
            f.write(f"| {category} | {count} | {total_pct:.1f}% | {risk_pct:.1f}% |\n")
        
        f.write(f"\n## 总体统计\n\n")
        f.write(f"- **总检测次数**: {sum(category_stats.values())}\n")
        f.write(f"- **平均每案例检测**: {sum(category_stats.values())/total_cases:.2f}\n")
        f.write(f"- **检测覆盖率**: {cases_with_any_hit/total_cases*100:.1f}%\n")
        
        # 排序显示
        f.write(f"\n## 检测频率排序\n\n")
        sorted_categories = sorted(category_stats.items(), key=lambda x: x[1], reverse=True)
        for i, (category, count) in enumerate(sorted_categories, 1):
            f.write(f"{i}. **{category}**: {count} 次\n")
    
    print(f"✅ 统计报告已保存: {report_file}")
    
    return csv_file, json_file, report_file

def print_summary(category_stats, total_cases):
    """打印摘要统计"""
    
    print(f"\n📈 六个类别检测统计:")
    print("-" * 50)
    
    for category in TARGET_CATEGORIES:
        count = category_stats[category]
        percentage = (count / total_cases) * 100
        print(f"{category:<15}: {count:>4} 案例 ({percentage:>5.1f}%)")
    
    total_hits = sum(category_stats.values())
    cases_with_hits = len(set(category_stats.keys()))
    
    print(f"\n🎯 总体统计:")
    print("-" * 30)
    print(f"总检测次数: {total_hits}")
    print(f"平均每案例检测: {total_hits/total_cases:.2f}")
    
    # 最高频的类别
    top_category = max(category_stats.items(), key=lambda x: x[1])
    print(f"最高频类别: {top_category[0]} ({top_category[1]} 次)")

def main():
    """主函数"""
    
    # 输入文件
    input_file = "results/individual_cases_results_20250811_104748.json"
    
    if not Path(input_file).exists():
        print(f"❌ 输入文件不存在: {input_file}")
        return
    
    # 提取结果
    print("🔍 开始提取六个类别的检测结果...")
    results, category_stats = extract_six_categories_results(input_file)
    
    # 打印摘要
    print_summary(category_stats, len(results))
    
    # 保存结果
    print(f"\n💾 保存结果到文件...")
    csv_file, json_file, report_file = save_results(results, category_stats)
    
    print(f"\n" + "="*60)
    print("✅ 处理完成！生成的文件:")
    print(f"📊 CSV数据文件: {csv_file}")
    print(f"📄 JSON数据文件: {json_file}")
    print(f"📋 统计报告: {report_file}")
    print("="*60)

if __name__ == "__main__":
    main()
