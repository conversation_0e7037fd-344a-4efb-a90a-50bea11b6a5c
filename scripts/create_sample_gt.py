#!/usr/bin/env python3
"""
创建示例ground truth数据，用于测试评估工具
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import random

# 小类别列表
SMALL_CLASSES = [
    "政府机构",
    "政府邮箱", 
    "key_contact",
    "internal_system",
    "咨询公司信息",
    "兜售用户信息",
    "负面新闻",
    "重大客诉",
    "索要联系方式",
    "辱骂信息",
]

def create_sample_ground_truth(output_dir: str = "data/gt/processed"):
    """创建示例ground truth数据"""
    
    # 从现有的ground_truth.jsonl读取案例
    gt_path = Path(output_dir) / "ground_truth.jsonl"
    
    if not gt_path.exists():
        print(f"未找到现有的ground truth文件: {gt_path}")
        print("请先运行 build_ground_truth.py 生成基础数据")
        return
    
    # 读取现有案例
    cases = []
    with open(gt_path, 'r', encoding='utf-8') as f:
        for line in f:
            data = json.loads(line.strip())
            cases.append(data)
    
    print(f"读取了 {len(cases)} 个案例")
    
    # 为前100个案例创建示例标注
    sample_cases = cases[:100]
    
    # 创建示例标注数据
    gt_with_labels = []
    labels_data = []
    
    for case in sample_cases:
        case_id = case['case_id']
        
        # 创建随机标注（实际使用时应该是真实的人工标注）
        labels = {}
        for cls in SMALL_CLASSES:
            # 随机分配标签，但保持合理的分布
            if cls in ["政府机构", "政府邮箱"]:
                labels[cls] = random.choice([0, 0, 0, 1])  # 25%概率为正例
            elif cls in ["辱骂信息", "负面新闻"]:
                labels[cls] = random.choice([0, 0, 1])  # 33%概率为正例
            else:
                labels[cls] = random.choice([0, 0, 0, 0, 1])  # 20%概率为正例
        
        # 添加标签到案例
        case_with_labels = case.copy()
        case_with_labels['labels'] = labels
        gt_with_labels.append(case_with_labels)
        
        # 准备CSV格式的标签数据
        label_row = {'case_id': case_id}
        label_row.update(labels)
        labels_data.append(label_row)
    
    # 保存带标签的JSONL文件
    sample_gt_path = Path(output_dir) / "sample_ground_truth.jsonl"
    with open(sample_gt_path, 'w', encoding='utf-8') as f:
        for case in gt_with_labels:
            f.write(json.dumps(case, ensure_ascii=False) + '\n')
    
    # 保存CSV格式的标签文件
    sample_labels_path = Path(output_dir) / "sample_labels.csv"
    df = pd.DataFrame(labels_data)
    df.to_csv(sample_labels_path, index=False, encoding='utf-8')
    
    print(f"✅ 创建了 {len(gt_with_labels)} 个示例标注案例")
    print(f"✅ JSONL格式: {sample_gt_path}")
    print(f"✅ CSV格式: {sample_labels_path}")
    
    # 打印标签分布统计
    print(f"\n标签分布统计:")
    for cls in SMALL_CLASSES:
        positive_count = sum(1 for labels in labels_data if labels[cls] == 1)
        print(f"  {cls}: {positive_count}/{len(labels_data)} ({positive_count/len(labels_data)*100:.1f}%)")

def create_sample_model_results(output_dir: str = "data/gt/processed"):
    """创建示例模型结果，用于测试评估工具"""
    
    sample_labels_path = Path(output_dir) / "sample_labels.csv"
    if not sample_labels_path.exists():
        print("请先运行 create_sample_ground_truth")
        return
    
    # 读取示例标签
    df_gt = pd.read_csv(sample_labels_path)
    
    # 创建模拟的模型结果（添加一些噪声）
    model_results = []
    
    for _, row in df_gt.iterrows():
        case_id = row['case_id']
        
        # 模拟模型预测（在真实标签基础上添加噪声）
        pred_labels = {}
        for cls in SMALL_CLASSES:
            gt_label = row[cls]
            
            # 模拟模型准确率约80%
            if random.random() < 0.8:
                pred_labels[cls] = gt_label  # 正确预测
            else:
                pred_labels[cls] = 1 - gt_label  # 错误预测
        
        model_result = {'case_id': case_id}
        model_result.update(pred_labels)
        model_results.append(model_result)
    
    # 保存模型结果
    model_results_path = Path(output_dir) / "sample_model_results.csv"
    df_model = pd.DataFrame(model_results)
    df_model.to_csv(model_results_path, index=False, encoding='utf-8')
    
    print(f"✅ 创建了 {len(model_results)} 个示例模型结果")
    print(f"✅ 保存到: {model_results_path}")

def main():
    """主函数"""
    output_dir = "data/gt/processed"
    
    print("创建示例ground truth数据...")
    create_sample_ground_truth(output_dir)
    
    print("\n创建示例模型结果...")
    create_sample_model_results(output_dir)
    
    print(f"\n现在可以使用以下命令测试评估工具:")
    print(f"uv run python scripts/evaluate_accuracy.py \\")
    print(f"  --gt data/gt/processed/sample_labels.csv \\")
    print(f"  --pred data/gt/processed/sample_model_results.csv \\")
    print(f"  --output data/gt/processed/evaluation_results.json")

if __name__ == "__main__":
    main()
